<?php

namespace App\Controllers;

use App\Models\PengumumanModel;

class <PERSON><PERSON>uman extends BaseController
{
    protected $pengumumanModel;

    public function __construct()
    {
        $this->pengumumanModel = new PengumumanModel();
    }

    public function index()
    {
        $search = $this->request->getGet('search');
        $status = $this->request->getGet('status');
        $penting = $this->request->getGet('penting');
        $perPage = 15;

        $data_pengumuman = $this->pengumumanModel->getPengumumanWithFilter($search, $status, $penting, $perPage);
        $pager = $this->pengumumanModel->pager;
        $statistics = $this->pengumumanModel->getStatistics();

        $data = [
            'title' => 'Pengumuman - SMART',
            'data_pengumuman' => $data_pengumuman,
            'pager' => $pager,
            'statistics' => $statistics,
            'search' => $search,
            'status' => $status,
            'penting' => $penting,
            'pengumumanModel' => $this->pengumumanModel
        ];

        return view('pengumuman/index', $data);
    }

    public function create()
    {
        // Only admin can create
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/info?tab=pengumuman')->with('error', 'Akses ditolak!');
        }

        $data = [
            'title' => 'Tambah Pengumuman - SMART'
        ];

        return view('pengumuman/create', $data);
    }

    public function store()
    {
        // Only admin can store
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/info?tab=pengumuman')->with('error', 'Akses ditolak!');
        }

        $rules = [
            'judul' => 'required|min_length[5]|max_length[200]',
            'isi' => 'required|min_length[10]',
            'tanggal_mulai' => 'required|valid_date',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'judul' => $this->request->getPost('judul'),
            'isi' => $this->request->getPost('isi'),
            'tanggal_mulai' => $this->request->getPost('tanggal_mulai'),
            'tanggal_selesai' => $this->request->getPost('tanggal_selesai') ?: null,
            'is_penting' => $this->request->getPost('is_penting') ? 1 : 0,
            'is_active' => 1,
            'created_by' => session()->get('user_id')
        ];

        if ($this->pengumumanModel->save($data)) {
            return redirect()->to('/pengumuman')->with('success', 'Pengumuman berhasil ditambahkan!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan pengumuman!');
        }
    }

    public function show($id)
    {
        $pengumuman = $this->pengumumanModel->find($id);
        
        if (!$pengumuman) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Pengumuman tidak ditemukan');
        }

        $data = [
            'title' => 'Detail Pengumuman - SMART',
            'pengumuman' => $pengumuman,
            'pengumumanModel' => $this->pengumumanModel
        ];

        return view('pengumuman/show', $data);
    }

    public function edit($id)
    {
        // Only admin can edit
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/info?tab=pengumuman')->with('error', 'Akses ditolak!');
        }

        $pengumuman = $this->pengumumanModel->find($id);
        
        if (!$pengumuman) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Pengumuman tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Pengumuman - SMART',
            'pengumuman' => $pengumuman
        ];

        return view('pengumuman/edit', $data);
    }

    public function update($id)
    {
        // Only admin can update
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/info?tab=pengumuman')->with('error', 'Akses ditolak!');
        }

        $pengumuman = $this->pengumumanModel->find($id);

        if (!$pengumuman) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Pengumuman tidak ditemukan');
        }

        $rules = [
            'judul' => 'required|min_length[5]|max_length[200]',
            'isi' => 'required|min_length[10]',
            'tanggal_mulai' => 'required|valid_date',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'judul' => $this->request->getPost('judul'),
            'isi' => $this->request->getPost('isi'),
            'tanggal_mulai' => $this->request->getPost('tanggal_mulai'),
            'tanggal_selesai' => $this->request->getPost('tanggal_selesai') ?: null,
            'is_penting' => $this->request->getPost('is_penting') ? 1 : 0
        ];

        if ($this->pengumumanModel->update($id, $data)) {
            return redirect()->to('/pengumuman')->with('success', 'Pengumuman berhasil diperbarui!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal memperbarui pengumuman!');
        }
    }

    public function delete($id)
    {
        // Only admin can delete
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/info?tab=pengumuman')->with('error', 'Akses ditolak!');
        }

        $pengumuman = $this->pengumumanModel->find($id);
        
        if (!$pengumuman) {
            return redirect()->to('/pengumuman')->with('error', 'Pengumuman tidak ditemukan!');
        }

        if ($this->pengumumanModel->delete($id)) {
            return redirect()->to('/pengumuman')->with('success', 'Pengumuman berhasil dihapus!');
        } else {
            return redirect()->to('/pengumuman')->with('error', 'Gagal menghapus pengumuman!');
        }
    }

    public function toggleStatus($id)
    {
        if ($this->pengumumanModel->toggleStatus($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Status pengumuman berhasil diubah!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Gagal mengubah status pengumuman!']);
        }
    }

    public function togglePenting($id)
    {
        if ($this->pengumumanModel->togglePenting($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Status penting berhasil diubah!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Gagal mengubah status penting!']);
        }
    }
}
