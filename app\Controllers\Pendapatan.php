<?php

namespace App\Controllers;

use App\Models\PendapatanModel;
use App\Models\KependudukanModel;
use App\Models\PendapatanLainnyaModel;

class Pendapatan extends BaseController
{
    protected $pendapatanModel;
    protected $kependudukanModel;
    protected $settingsModel;
    protected $pendapatanLainnyaModel;

    public function __construct()
    {
        $this->pendapatanModel = new PendapatanModel();
        $this->kependudukanModel = new KependudukanModel();
        $this->pendapatanLainnyaModel = new PendapatanLainnyaModel();
        $this->settingsModel = new \App\Models\SettingsModel();
    }

    public function index()
    {
        $search = $this->request->getGet('search');
        $jenis = $this->request->getGet('jenis');
        $bulan = $this->request->getGet('bulan');
        $tahun = $this->request->getGet('tahun') ?: date('Y');
        $perPage = 15;

        $builder = $this->pendapatanModel;

        // Apply filters
        if ($search) {
            $builder = $builder->like('nama_pembayar', $search);
        }

        if ($jenis) {
            $builder = $builder->where('jenis_pendapatan', $jenis);
        }

        if ($bulan) {
            $builder = $builder->where('bulan', $bulan);
        }

        if ($tahun) {
            $builder = $builder->where('tahun', $tahun);
        }

        $data_pendapatan = $builder->orderBy('tanggal_bayar', 'DESC')->paginate($perPage);
        $pager = $this->pendapatanModel->pager;

        // Get statistik per jenis pendapatan
        $statistikIuranWarga = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_warga', $bulan, $tahun);
        $statistikIuranSampah = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_sampah', $bulan, $tahun);
        $statistikJimpitan = $this->pendapatanModel->getTotalPendapatanByJenis('jimpitan', $bulan, $tahun);

        // Fix untuk pendapatan lainnya - jika tidak ada filter bulan, ambil data tahunan
        if ($bulan) {
            $statistikLainnya = $this->pendapatanLainnyaModel->getTotalPendapatanBulanan($bulan, $tahun);
        } else {
            // Ambil total untuk tahun ini
            $statistikLainnya = $this->pendapatanLainnyaModel->where('YEAR(tanggal)', $tahun)->selectSum('jumlah', 'total')->first()['total'] ?? 0;
        }

        // Hitung persentase berdasarkan data bulan sebelumnya
        $bulanSebelumnya = $bulan ? ($bulan == 1 ? 12 : $bulan - 1) : date('n') - 1;
        $tahunSebelumnya = ($bulan == 1) ? $tahun - 1 : $tahun;

        $statistikIuranWargaSebelumnya = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_warga', $bulanSebelumnya, $tahunSebelumnya);
        $statistikIuranSampahSebelumnya = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_sampah', $bulanSebelumnya, $tahunSebelumnya);
        $statistikJimpitanSebelumnya = $this->pendapatanModel->getTotalPendapatanByJenis('jimpitan', $bulanSebelumnya, $tahunSebelumnya);
        $statistikLainnyaSebelumnya = $bulanSebelumnya ? $this->pendapatanLainnyaModel->getTotalPendapatanBulanan($bulanSebelumnya, $tahunSebelumnya) : 0;

        // Hitung persentase pertumbuhan
        $persentaseIuranWarga = $this->hitungPersentase($statistikIuranWarga, $statistikIuranWargaSebelumnya);
        $persentaseIuranSampah = $this->hitungPersentase($statistikIuranSampah, $statistikIuranSampahSebelumnya);
        $persentaseJimpitan = $this->hitungPersentase($statistikJimpitan, $statistikJimpitanSebelumnya);
        $persentaseLainnya = $this->hitungPersentase($statistikLainnya, $statistikLainnyaSebelumnya);

        // Get data untuk chart (7 hari terakhir atau bulanan)
        $chartData = $this->getChartData($bulan, $tahun);

        // Get data pendapatan lainnya untuk ditampilkan di tabel terpisah
        $builderLainnya = $this->pendapatanLainnyaModel;
        if ($bulan && $tahun) {
            $builderLainnya = $builderLainnya->where('MONTH(tanggal)', $bulan)
                                           ->where('YEAR(tanggal)', $tahun);
        } elseif ($tahun) {
            $builderLainnya = $builderLainnya->where('YEAR(tanggal)', $tahun);
        }
        $data_pendapatan_lainnya = $builderLainnya->orderBy('tanggal', 'DESC')->findAll();

        $data = [
            'title' => 'Data Pendapatan - SMART',
            'data_pendapatan' => $data_pendapatan,
            'data_pendapatan_lainnya' => $data_pendapatan_lainnya,
            'pager' => $pager,
            'search' => $search,
            'jenis' => $jenis,
            'bulan' => $bulan,
            'tahun' => $tahun,
            'statistik_iuran_warga' => $statistikIuranWarga,
            'statistik_iuran_sampah' => $statistikIuranSampah,
            'statistik_jimpitan' => $statistikJimpitan,
            'statistik_lainnya' => $statistikLainnya,
            'persentase_iuran_warga' => $persentaseIuranWarga,
            'persentase_iuran_sampah' => $persentaseIuranSampah,
            'persentase_jimpitan' => $persentaseJimpitan,
            'persentase_lainnya' => $persentaseLainnya,
            'chart_data' => $chartData,
            'pendapatanModel' => $this->pendapatanModel,
            'pendapatanLainnyaModel' => $this->pendapatanLainnyaModel
        ];

        return view('pendapatan/index', $data);
    }

    private function getChartData($bulan, $tahun)
    {
        // Generate data untuk 7 hari terakhir jika bulan dan tahun adalah bulan/tahun saat ini
        $currentMonth = date('n');
        $currentYear = date('Y');

        if ($bulan == $currentMonth && $tahun == $currentYear) {
            // 7 hari terakhir
            $chartData = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $dayName = date('D', strtotime($date));

                // Get total pendapatan untuk tanggal ini
                $totalIuran = $this->pendapatanModel->where('tanggal_bayar', $date)->selectSum('jumlah')->first()['jumlah'] ?? 0;
                $totalLainnya = $this->pendapatanLainnyaModel->where('tanggal', $date)->selectSum('jumlah')->first()['jumlah'] ?? 0;

                $chartData[] = [
                    'label' => $dayName,
                    'value' => $totalIuran + $totalLainnya
                ];
            }
        } else {
            // Data bulanan dalam tahun
            $chartData = [];
            for ($i = 1; $i <= 12; $i++) {
                $totalIuran = $this->pendapatanModel->getTotalPendapatanBulanan($i, $tahun);
                $totalLainnya = $this->pendapatanLainnyaModel->getTotalPendapatanBulanan($i, $tahun);

                $chartData[] = [
                    'label' => substr($this->pendapatanModel->getNamaBulan($i), 0, 3),
                    'value' => $totalIuran + $totalLainnya
                ];
            }
        }

        return $chartData;
    }

    private function hitungPersentase($nilaiSekarang, $nilaiSebelumnya)
    {
        if ($nilaiSebelumnya == 0) {
            return $nilaiSekarang > 0 ? 100 : 0;
        }

        $persentase = (($nilaiSekarang - $nilaiSebelumnya) / $nilaiSebelumnya) * 100;
        return round($persentase, 1);
    }

    public function create()
    {
        $jenis = $this->request->getGet('jenis');

        // Untuk jimpitan, hanya tampilkan KK yang wajib iuran
        if ($jenis === 'jimpitan') {
            $kepalaKeluarga = $this->kependudukanModel->getKepalaKeluargaWajibIuran();
        } else {
            $kepalaKeluarga = $this->kependudukanModel->getKepalaKeluarga();
        }

        // Get nominal iuran dari settings
        $iuran_settings = $this->settingsModel->getIuranSettings();

        $data = [
            'title' => 'Tambah Pendapatan - SMART',
            'jenis' => $jenis,
            'kepala_keluarga' => $kepalaKeluarga,
            'pendapatanModel' => $this->pendapatanModel,
            'iuran_settings' => $iuran_settings
        ];

        return view('pendapatan/create', $data);
    }

    public function store()
    {
        $validation = \Config\Services::validation();

        $rules = [
            'jenis_pendapatan' => 'required|in_list[jimpitan,lainnya]',
            'nama_pembayar' => 'required|min_length[3]|max_length[100]',
            'jumlah' => 'required|decimal|greater_than[0]',
            'tanggal_bayar' => 'required|valid_date',
        ];

        // Add conditional validation for bulan and tahun
        $jenis = $this->request->getPost('jenis_pendapatan');
        if (in_array($jenis, ['jimpitan'])) {
            $rules['no_kk'] = 'required';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'jenis_pendapatan' => $this->request->getPost('jenis_pendapatan'),
            'no_kk' => $this->request->getPost('no_kk'),
            'nama_pembayar' => $this->request->getPost('nama_pembayar'),
            'jumlah' => $this->request->getPost('jumlah'),
            'bulan' => $this->request->getPost('bulan'),
            'tahun' => $this->request->getPost('tahun'),
            'tanggal_bayar' => $this->request->getPost('tanggal_bayar'),
            'keterangan' => $this->request->getPost('keterangan'),
            'created_by' => session()->get('user_id')
        ];

        // No need to check for duplicate payments since iuran payments are now handled via laporan

        if ($this->pendapatanModel->save($data)) {
            return redirect()->to('/pendapatan')->with('success', 'Data pendapatan berhasil ditambahkan!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan data pendapatan!');
        }
    }

    public function edit($id)
    {
        $pendapatan = $this->pendapatanModel->find($id);
        
        if (!$pendapatan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data pendapatan tidak ditemukan');
        }

        $kepalaKeluarga = $this->kependudukanModel->getKepalaKeluarga();

        $data = [
            'title' => 'Edit Pendapatan - SMART',
            'pendapatan' => $pendapatan,
            'kepala_keluarga' => $kepalaKeluarga,
            'pendapatanModel' => $this->pendapatanModel
        ];

        return view('pendapatan/edit', $data);
    }

    public function update($id)
    {
        $pendapatan = $this->pendapatanModel->find($id);

        if (!$pendapatan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data pendapatan tidak ditemukan');
        }

        $validation = \Config\Services::validation();

        $rules = [
            'jenis_pendapatan' => 'required|in_list[jimpitan,lainnya]',
            'nama_pembayar' => 'required|min_length[3]|max_length[100]',
            'jumlah' => 'required|decimal|greater_than[0]',
            'tanggal_bayar' => 'required|valid_date',
        ];

        // Add conditional validation for bulan and tahun
        $jenis = $this->request->getPost('jenis_pendapatan');
        if (in_array($jenis, ['jimpitan'])) {
            $rules['no_kk'] = 'required';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'jenis_pendapatan' => $this->request->getPost('jenis_pendapatan'),
            'no_kk' => $this->request->getPost('no_kk'),
            'nama_pembayar' => $this->request->getPost('nama_pembayar'),
            'jumlah' => $this->request->getPost('jumlah'),
            'bulan' => $this->request->getPost('bulan'),
            'tahun' => $this->request->getPost('tahun'),
            'tanggal_bayar' => $this->request->getPost('tanggal_bayar'),
            'keterangan' => $this->request->getPost('keterangan')
        ];

        // No need to check for duplicate payments since iuran payments are now handled via laporan

        if ($this->pendapatanModel->update($id, $data)) {
            return redirect()->to('/pendapatan')->with('success', 'Data pendapatan berhasil diperbarui!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal memperbarui data pendapatan!');
        }
    }

    public function delete($id)
    {
        $pendapatan = $this->pendapatanModel->find($id);
        
        if (!$pendapatan) {
            return redirect()->to('/pendapatan')->with('error', 'Data pendapatan tidak ditemukan!');
        }

        if ($this->pendapatanModel->delete($id)) {
            return redirect()->to('/pendapatan')->with('success', 'Data pendapatan berhasil dihapus!');
        } else {
            return redirect()->to('/pendapatan')->with('error', 'Gagal menghapus data pendapatan!');
        }
    }

    public function getKepalaKeluargaByKK()
    {
        $no_kk = $this->request->getGet('no_kk');
        
        if (!$no_kk) {
            return $this->response->setJSON(['error' => 'No KK required']);
        }

        $kepalaKeluarga = $this->kependudukanModel->getKepalaKeluargaByNoKK($no_kk);
        
        if ($kepalaKeluarga) {
            return $this->response->setJSON([
                'success' => true,
                'data' => $kepalaKeluarga
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Kepala keluarga tidak ditemukan'
            ]);
        }
    }
}
