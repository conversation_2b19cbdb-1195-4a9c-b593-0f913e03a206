<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\KependudukanModel;

class Profile extends BaseController
{
    protected $userModel;
    protected $kependudukanModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->kependudukanModel = new KependudukanModel();
    }

    public function index()
    {
        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);
        
        $kependudukan = null;
        if ($user['nik']) {
            $kependudukan = $this->kependudukanModel->where('nik', $user['nik'])->first();
        }

        $data = [
            'title' => 'Profil Pengguna - SMART',
            'user' => $user,
            'kependudukan' => $kependudukan
        ];

        // Jika admin, ambil daftar admin
        if (session()->get('role') === 'admin') {
            $data['admin_list'] = $this->userModel->where('role', 'admin')->findAll();
        }

        return view('profile/index', $data);
    }

    public function update()
    {
        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->back()->with('error', 'User tidak ditemukan!');
        }

        $rules = [
            'username' => "required|min_length[3]|max_length[50]|is_unique[users.username,id,{$userId}]"
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'username' => $this->request->getPost('username')
        ];

        if ($this->userModel->update($userId, $updateData)) {
            // Update session data
            session()->set('username', $updateData['username']);
            
            return redirect()->to('/profile')->with('success', 'Profil berhasil diperbarui!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal memperbarui profil!');
        }
    }

    public function tambahAdmin()
    {
        // Only admin can add new admin
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/dashboard')->with('error', 'Akses ditolak!');
        }

        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
            'password' => 'required|min_length[6]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'username' => $this->request->getPost('username'),
            'password' => $this->request->getPost('password'), // Akan di-hash otomatis oleh UserModel callback
            'role' => 'admin',
            'nik' => null,
            'is_active' => 1
        ];

        if ($this->userModel->insert($data)) {
            return redirect()->to('/profile')->with('success', 'Admin baru berhasil ditambahkan!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan admin baru!');
        }
    }

    public function editAdmin()
    {
        // Only admin can edit admin
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/dashboard')->with('error', 'Akses ditolak!');
        }

        $adminId = $this->request->getPost('admin_id');
        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        // Validasi username
        $existingUser = $this->userModel->where('username', $username)
                                       ->where('id !=', $adminId)
                                       ->first();

        if ($existingUser) {
            return redirect()->back()->withInput()->with('error', 'Username sudah digunakan!');
        }

        if (empty($username) || strlen($username) < 3) {
            return redirect()->back()->withInput()->with('error', 'Username minimal 3 karakter!');
        }

        if (!empty($password) && strlen($password) < 6) {
            return redirect()->back()->withInput()->with('error', 'Password minimal 6 karakter!');
        }

        $data = [
            'username' => $username
        ];

        if (!empty($password)) {
            $data['password'] = $password; // Akan di-hash otomatis oleh UserModel callback
            log_message('info', 'Updating admin password for ID: ' . $adminId . ', Raw password: ' . $password);
        }

        if ($this->userModel->update($adminId, $data)) {
            log_message('info', 'Admin updated successfully for ID: ' . $adminId);
            return redirect()->to('/profile')->with('success', 'Admin berhasil diupdate!');
        } else {
            log_message('error', 'Failed to update admin for ID: ' . $adminId);
            return redirect()->back()->withInput()->with('error', 'Gagal mengupdate admin!');
        }
    }

    public function hapusAdmin()
    {
        // Only admin can delete admin
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/dashboard')->with('error', 'Akses ditolak!');
        }

        $adminId = $this->request->getPost('admin_id');
        $currentUserId = session()->get('user_id');

        // Tidak bisa hapus diri sendiri
        if ($adminId == $currentUserId) {
            return redirect()->back()->with('error', 'Tidak dapat menghapus akun sendiri!');
        }

        if ($this->userModel->delete($adminId)) {
            return redirect()->to('/profile')->with('success', 'Admin berhasil dihapus!');
        } else {
            return redirect()->back()->with('error', 'Gagal menghapus admin!');
        }
    }

    public function toggleAdminStatus()
    {
        // Only admin can toggle admin status
        if (session()->get('role') !== 'admin') {
            return $this->response->setJSON(['success' => false, 'message' => 'Akses ditolak!']);
        }

        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $input = $this->request->getJSON(true);
        $adminId = $input['admin_id'] ?? null;
        $isActive = $input['is_active'] ?? null;
        $currentUserId = session()->get('user_id');

        // Tidak bisa nonaktifkan diri sendiri
        if ($adminId == $currentUserId && !$isActive) {
            return $this->response->setJSON(['success' => false, 'message' => 'Tidak dapat menonaktifkan akun sendiri!']);
        }

        if ($this->userModel->update($adminId, ['is_active' => $isActive])) {
            $status = $isActive ? 'diaktifkan' : 'dinonaktifkan';
            return $this->response->setJSON(['success' => true, 'message' => "Admin berhasil {$status}!"]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Gagal mengubah status admin!']);
        }
    }
}
