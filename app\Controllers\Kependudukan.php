<?php

namespace App\Controllers;

use App\Models\KependudukanModel;
use App\Models\UserModel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Kependudukan extends BaseController
{
    protected $kependudukanModel;
    protected $settingsModel;
    protected $userModel;

    public function __construct()
    {
        $this->kependudukanModel = new KependudukanModel();
        $this->userModel = new UserModel();
        $this->settingsModel = new \App\Models\SettingsModel();
    }

    public function index()
    {
        $search = $this->request->getGet('search');
        $perPage = 10;

        if ($search) {
            $data_kependudukan = $this->kependudukanModel->searchPenduduk($search);
            $pager = null;
        } else {
            $data_kependudukan = $this->kependudukanModel->paginate($perPage);
            $pager = $this->kependudukanModel->pager;
        }

        $data = [
            'title' => 'Data Kependudukan - SMART',
            'data_kependudukan' => $data_kependudukan,
            'pager' => $pager,
            'search' => $search
        ];

        return view('kependudukan/index', $data);
    }

    public function kepalaKeluarga()
    {
        $data = [
            'title' => 'Daftar Kepala Keluarga - SMART',
            'daftar_kk' => $this->kependudukanModel->getDaftarKepalaKeluarga()
        ];

        return view('kependudukan/kepala_keluarga', $data);
    }

    public function updateWajibIuran()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $id = $this->request->getPost('id');
        $wajib_iuran = $this->request->getPost('wajib_iuran');

        try {
            $result = $this->kependudukanModel->updateWajibIuran($id, $wajib_iuran);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Status wajib iuran berhasil diperbarui'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal memperbarui status wajib iuran'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function printKepalaKeluarga()
    {
        $data = [
            'title' => 'Daftar Kepala Keluarga',
            'daftar_kk' => $this->kependudukanModel->getDaftarKepalaKeluarga(),
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('kependudukan/print_kepala_keluarga', $data);
    }

    public function view($id)
    {
        $penduduk = $this->kependudukanModel->find($id);
        
        if (!$penduduk) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data penduduk tidak ditemukan');
        }

        // Get keluarga dengan KK yang sama
        $keluarga = $this->kependudukanModel->getKeluargaByKK($penduduk['no_kk']);

        $data = [
            'title' => 'Detail Penduduk - SMART',
            'penduduk' => $penduduk,
            'keluarga' => $keluarga
        ];

        return view('kependudukan/view', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Tambah Data Kependudukan - SMART'
        ];

        return view('kependudukan/create', $data);
    }

    public function store()
    {
        $validation = \Config\Services::validation();

        $rules = [
            'no_kk' => 'required|exact_length[16]',
            'no_rumah' => 'permit_empty|max_length[20]',
            'nama_lengkap' => 'required|min_length[3]|max_length[100]',
            'nik' => 'required|exact_length[16]|is_unique[kependudukan.nik]',
            'jenis_kelamin' => 'required|in_list[L,P]',
            'tempat_lahir' => 'required|max_length[50]',
            'tanggal_lahir' => 'required|valid_date',
            'agama' => 'required|max_length[20]',
            'pendidikan' => 'required|max_length[50]',
            'jenis_pekerjaan' => 'required|max_length[50]',
            'status_pernikahan' => 'required|in_list[Belum Kawin,Kawin,Cerai Hidup,Cerai Mati]',
            'status_hubungan_keluarga' => 'required|max_length[30]',
            'kewarganegaraan' => 'permit_empty|max_length[20]',
            'nama_ayah' => 'permit_empty|max_length[100]',
            'nama_ibu' => 'permit_empty|max_length[100]',
            'no_telp' => 'permit_empty|max_length[15]',
            'jalan' => 'permit_empty|max_length[100]',
            'rt' => 'permit_empty|max_length[3]',
            'rw' => 'permit_empty|max_length[3]',
            'desa' => 'permit_empty|max_length[50]',
            'kecamatan' => 'permit_empty|max_length[50]',
            'kabupaten' => 'permit_empty|max_length[50]',
            'kode_pos' => 'permit_empty|max_length[5]'
        ];

        $validation->setRules($rules);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'no_kk' => $this->request->getPost('no_kk'),
            'no_rumah' => $this->request->getPost('no_rumah'),
            'nama_lengkap' => $this->request->getPost('nama_lengkap'),
            'nik' => $this->request->getPost('nik'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'tempat_lahir' => $this->request->getPost('tempat_lahir'),
            'tanggal_lahir' => $this->request->getPost('tanggal_lahir'),
            'agama' => $this->request->getPost('agama'),
            'pendidikan' => $this->request->getPost('pendidikan'),
            'jenis_pekerjaan' => $this->request->getPost('jenis_pekerjaan'),
            'status_pernikahan' => $this->request->getPost('status_pernikahan'),
            'status_hubungan_keluarga' => $this->request->getPost('status_hubungan_keluarga'),
            'kewarganegaraan' => $this->request->getPost('kewarganegaraan'),
            'nama_ayah' => $this->request->getPost('nama_ayah'),
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'no_telp' => $this->request->getPost('no_telp'),
            'jalan' => $this->request->getPost('jalan'),
            'rt' => $this->request->getPost('rt'),
            'rw' => $this->request->getPost('rw'),
            'desa' => $this->request->getPost('desa'),
            'kecamatan' => $this->request->getPost('kecamatan'),
            'kabupaten' => $this->request->getPost('kabupaten'),
            'kode_pos' => $this->request->getPost('kode_pos')
        ];

        try {
            $result = $this->kependudukanModel->insert($data);
            if ($result) {
                return redirect()->to('/kependudukan')->with('success', 'Data kependudukan berhasil ditambahkan!');
            } else {
                $errors = $this->kependudukanModel->errors();
                $errorMessage = 'Gagal menambahkan data kependudukan!';
                if (!empty($errors)) {
                    $errorMessage .= ' Error: ' . implode(', ', $errors);
                }
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $penduduk = $this->kependudukanModel->find($id);
        
        if (!$penduduk) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data penduduk tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Kependudukan - SMART',
            'penduduk' => $penduduk
        ];

        return view('kependudukan/edit', $data);
    }

    public function update($id)
    {
        $penduduk = $this->kependudukanModel->find($id);

        if (!$penduduk) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data penduduk tidak ditemukan');
        }

        // Validation rules without is_unique for NIK
        $rules = [
            'no_kk' => 'required|exact_length[16]',
            'no_rumah' => 'permit_empty|max_length[20]',
            'nama_lengkap' => 'required|min_length[3]|max_length[100]',
            'nik' => 'required|exact_length[16]',
            'jenis_kelamin' => 'required|in_list[L,P]',
            'tempat_lahir' => 'required|max_length[50]',
            'tanggal_lahir' => 'required|valid_date',
            'agama' => 'required|max_length[20]',
            'pendidikan' => 'required|max_length[50]',
            'jenis_pekerjaan' => 'required|max_length[50]',
            'status_pernikahan' => 'required|in_list[Belum Kawin,Kawin,Cerai Hidup,Cerai Mati]',
            'status_hubungan_keluarga' => 'required|max_length[30]',
            'kewarganegaraan' => 'permit_empty|max_length[20]',
            'nama_ayah' => 'permit_empty|max_length[100]',
            'nama_ibu' => 'permit_empty|max_length[100]',
            'no_telp' => 'permit_empty|max_length[15]',
            'jalan' => 'permit_empty|max_length[100]',
            'rt' => 'permit_empty|max_length[3]',
            'rw' => 'permit_empty|max_length[3]',
            'desa' => 'permit_empty|max_length[50]',
            'kecamatan' => 'permit_empty|max_length[50]',
            'kabupaten' => 'permit_empty|max_length[50]',
            'kode_pos' => 'permit_empty|max_length[5]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Untuk update, tidak perlu cek NIK uniqueness sama sekali
        // User bisa edit data apapun termasuk NIK tanpa validasi uniqueness

        $data = [
            'no_kk' => $this->request->getPost('no_kk'),
            'no_rumah' => $this->request->getPost('no_rumah'),
            'nama_lengkap' => $this->request->getPost('nama_lengkap'),
            'nik' => $this->request->getPost('nik'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'tempat_lahir' => $this->request->getPost('tempat_lahir'),
            'tanggal_lahir' => $this->request->getPost('tanggal_lahir'),
            'agama' => $this->request->getPost('agama'),
            'pendidikan' => $this->request->getPost('pendidikan'),
            'jenis_pekerjaan' => $this->request->getPost('jenis_pekerjaan'),
            'status_pernikahan' => $this->request->getPost('status_pernikahan'),
            'status_hubungan_keluarga' => $this->request->getPost('status_hubungan_keluarga'),
            'kewarganegaraan' => $this->request->getPost('kewarganegaraan'),
            'nama_ayah' => $this->request->getPost('nama_ayah'),
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'no_telp' => $this->request->getPost('no_telp'),
            'jalan' => $this->request->getPost('jalan'),
            'rt' => $this->request->getPost('rt'),
            'rw' => $this->request->getPost('rw'),
            'desa' => $this->request->getPost('desa'),
            'kecamatan' => $this->request->getPost('kecamatan'),
            'kabupaten' => $this->request->getPost('kabupaten'),
            'kode_pos' => $this->request->getPost('kode_pos')
        ];

        try {
            $result = $this->kependudukanModel->update($id, $data);
            if ($result) {
                return redirect()->to('/kependudukan')->with('success', 'Data kependudukan berhasil diperbarui!');
            } else {
                $errors = $this->kependudukanModel->errors();
                $errorMessage = 'Gagal memperbarui data kependudukan!';
                if (!empty($errors)) {
                    $errorMessage .= ' Error: ' . implode(', ', $errors);
                }
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        $penduduk = $this->kependudukanModel->find($id);
        
        if (!$penduduk) {
            return redirect()->to('/kependudukan')->with('error', 'Data penduduk tidak ditemukan!');
        }

        // Delete related user account if exists
        $user = $this->userModel->where('nik', $penduduk['nik'])->first();
        if ($user) {
            $this->userModel->delete($user['id']);
        }

        if ($this->kependudukanModel->delete($id)) {
            return redirect()->to('/kependudukan')->with('success', 'Data kependudukan berhasil dihapus!');
        } else {
            return redirect()->to('/kependudukan')->with('error', 'Gagal menghapus data kependudukan!');
        }
    }

    public function search()
    {
        $keyword = $this->request->getGet('q');

        if (!$keyword) {
            return $this->response->setJSON([]);
        }

        $results = $this->kependudukanModel->searchPenduduk($keyword);

        return $this->response->setJSON($results);
    }

    public function print($id)
    {
        $penduduk = $this->kependudukanModel->find($id);

        if (!$penduduk) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data penduduk tidak ditemukan');
        }

        // Get profil RT
        $settingsModel = new \App\Models\SettingsModel();
        $profil_rt = $settingsModel->getProfilRTSettings();
        
        // Get keluarga data
        $keluarga = $this->kependudukanModel->where('no_kk', $penduduk['no_kk'])
                                           ->orderBy('status_hubungan_keluarga', 'ASC')
                                           ->findAll();

        $data = [
            'title' => 'Cetak Data Kependudukan - ' . $penduduk['nama_lengkap'],
            'penduduk' => $penduduk,
            'keluarga' => $keluarga,
            'profil_rt' => $profil_rt,
        ];

        return view('kependudukan/print', $data);
    }

    public function export()
    {
        $data_kependudukan = $this->kependudukanModel->findAll();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $headers = [
            'A1' => 'No',
            'B1' => 'No. KK',
            'C1' => 'No. Rumah',
            'D1' => 'NIK',
            'E1' => 'Nama Lengkap',
            'F1' => 'Jenis Kelamin',
            'G1' => 'Tempat Lahir',
            'H1' => 'Tanggal Lahir',
            'I1' => 'Agama',
            'J1' => 'Pendidikan',
            'K1' => 'Pekerjaan',
            'L1' => 'Status Pernikahan',
            'M1' => 'Status dalam Keluarga',
            'N1' => 'Kewarganegaraan',
            'O1' => 'Nama Ayah',
            'P1' => 'Nama Ibu',
            'Q1' => 'No. Telepon',
            'R1' => 'Jalan',
            'S1' => 'RT',
            'T1' => 'RW',
            'U1' => 'Desa/Kelurahan',
            'V1' => 'Kecamatan',
            'W1' => 'Kabupaten/Kota',
            'X1' => 'Kode Pos'
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // Style headers
        $sheet->getStyle('A1:X1')->getFont()->setBold(true);
        $sheet->getStyle('A1:X1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $sheet->getStyle('A1:X1')->getFill()->getStartColor()->setARGB('FFE2E2E2');

        // Fill data
        $row = 2;
        $no = 1;
        foreach ($data_kependudukan as $penduduk) {
            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $penduduk['no_kk']);
            $sheet->setCellValue('C' . $row, $penduduk['no_rumah'] ?? '');
            $sheet->setCellValue('D' . $row, $penduduk['nik']);
            $sheet->setCellValue('E' . $row, $penduduk['nama_lengkap']);
            $sheet->setCellValue('F' . $row, $penduduk['jenis_kelamin'] == 'L' ? 'Laki-laki' : 'Perempuan');
            $sheet->setCellValue('G' . $row, $penduduk['tempat_lahir']);
            $sheet->setCellValue('H' . $row, $penduduk['tanggal_lahir']);
            $sheet->setCellValue('I' . $row, $penduduk['agama']);
            $sheet->setCellValue('J' . $row, $penduduk['pendidikan']);
            $sheet->setCellValue('K' . $row, $penduduk['jenis_pekerjaan']);
            $sheet->setCellValue('L' . $row, $penduduk['status_pernikahan']);
            $sheet->setCellValue('M' . $row, $penduduk['status_hubungan_keluarga']);
            $sheet->setCellValue('N' . $row, $penduduk['kewarganegaraan']);
            $sheet->setCellValue('O' . $row, $penduduk['nama_ayah']);
            $sheet->setCellValue('P' . $row, $penduduk['nama_ibu']);
            $sheet->setCellValue('Q' . $row, $penduduk['no_telp']);
            $sheet->setCellValue('R' . $row, $penduduk['jalan']);
            $sheet->setCellValue('S' . $row, $penduduk['rt']);
            $sheet->setCellValue('T' . $row, $penduduk['rw']);
            $sheet->setCellValue('U' . $row, $penduduk['desa']);
            $sheet->setCellValue('V' . $row, $penduduk['kecamatan']);
            $sheet->setCellValue('W' . $row, $penduduk['kabupaten']);
            $sheet->setCellValue('X' . $row, $penduduk['kode_pos']);
            $row++;
        }

        // Auto size columns
        foreach (range('A', 'X') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);

        $filename = 'Data_Kependudukan_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function import()
    {
        $data = [
            'title' => 'Import Data Kependudukan - SMART'
        ];

        return view('kependudukan/import', $data);
    }

    public function processImport()
    {
        $file = $this->request->getFile('excel_file');

        if (!$file->isValid()) {
            return redirect()->back()->with('error', 'File tidak valid!');
        }

        if ($file->getExtension() !== 'xlsx') {
            return redirect()->back()->with('error', 'File harus berformat .xlsx!');
        }

        try {
            $spreadsheet = IOFactory::load($file->getTempName());
            $sheet = $spreadsheet->getActiveSheet();
            $data = $sheet->toArray();

            // Remove header row
            array_shift($data);

            $imported = 0;
            $errors = [];

            foreach ($data as $index => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Format nomor telepon
                $noTelp = $row[15] ?? '';
                if (!empty($noTelp)) {
                    $cleanNumber = preg_replace('/[^0-9]/', '', $noTelp);
                    // Jika nomor dimulai dengan 8 (tanpa 0), tambahkan 62
                    if (substr($cleanNumber, 0, 1) == '8' && strlen($cleanNumber) >= 10) {
                        $noTelp = '62' . $cleanNumber;
                    }
                    // Jika nomor dimulai dengan 08, ubah ke 628
                    elseif (substr($cleanNumber, 0, 2) == '08') {
                        $noTelp = '62' . substr($cleanNumber, 1);
                    }
                    // Jika sudah dimulai dengan 62, biarkan
                    elseif (substr($cleanNumber, 0, 2) == '62') {
                        $noTelp = $cleanNumber;
                    }
                    else {
                        $noTelp = $cleanNumber;
                    }
                }

                $rowData = [
                    'no_kk' => $row[1] ?? '',
                    'no_rumah' => $row[2] ?? '',
                    'nik' => $row[3] ?? '',
                    'nama_lengkap' => $row[4] ?? '',
                    'jenis_kelamin' => ($row[5] == 'Laki-laki') ? 'L' : 'P',
                    'tempat_lahir' => $row[6] ?? '',
                    'tanggal_lahir' => $row[7] ?? '',
                    'agama' => $row[8] ?? '',
                    'pendidikan' => $row[9] ?? '',
                    'jenis_pekerjaan' => $row[10] ?? '',
                    'status_pernikahan' => $row[11] ?? '',
                    'status_hubungan_keluarga' => $row[12] ?? '',
                    'kewarganegaraan' => $row[13] ?? 'WNI',
                    'nama_ayah' => $row[14] ?? '',
                    'nama_ibu' => $row[15] ?? '',
                    'no_telp' => $noTelp,
                    'jalan' => $row[17] ?? '',
                    'rt' => $row[18] ?? '',
                    'rw' => $row[19] ?? '',
                    'desa' => $row[20] ?? '',
                    'kecamatan' => $row[21] ?? '',
                    'kabupaten' => $row[22] ?? '',
                    'kode_pos' => $row[23] ?? ''
                ];

                // Validate required fields
                if (empty($rowData['no_kk']) || empty($rowData['nik']) || empty($rowData['nama_lengkap'])) {
                    $errors[] = "Baris " . ($index + 2) . ": No. KK, NIK, dan Nama Lengkap harus diisi";
                    continue;
                }

                // Check if NIK already exists
                $existing = $this->kependudukanModel->where('nik', $rowData['nik'])->first();
                if ($existing) {
                    $errors[] = "Baris " . ($index + 2) . ": NIK {$rowData['nik']} sudah terdaftar";
                    continue;
                }

                if ($this->kependudukanModel->insert($rowData)) {
                    $imported++;
                } else {
                    $errors[] = "Baris " . ($index + 2) . ": Gagal menyimpan data";
                }
            }

            $message = "Berhasil mengimpor {$imported} data";
            if (!empty($errors)) {
                $message .= ". " . count($errors) . " data gagal diimpor.";
                session()->setFlashdata('import_errors', $errors);
            }

            return redirect()->to('/kependudukan')->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function downloadTemplate()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $headers = [
            'A1' => 'No',
            'B1' => 'No. KK',
            'C1' => 'No. Rumah',
            'D1' => 'NIK',
            'E1' => 'Nama Lengkap',
            'F1' => 'Jenis Kelamin',
            'G1' => 'Tempat Lahir',
            'H1' => 'Tanggal Lahir',
            'I1' => 'Agama',
            'J1' => 'Pendidikan',
            'K1' => 'Pekerjaan',
            'L1' => 'Status Pernikahan',
            'M1' => 'Status dalam Keluarga',
            'N1' => 'Kewarganegaraan',
            'O1' => 'Nama Ayah',
            'P1' => 'Nama Ibu',
            'Q1' => 'No. Telepon',
            'R1' => 'Jalan',
            'S1' => 'RT',
            'T1' => 'RW',
            'U1' => 'Desa/Kelurahan',
            'V1' => 'Kecamatan',
            'W1' => 'Kabupaten/Kota',
            'X1' => 'Kode Pos'
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // Style headers
        $sheet->getStyle('A1:X1')->getFont()->setBold(true);
        $sheet->getStyle('A1:X1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $sheet->getStyle('A1:X1')->getFill()->getStartColor()->setARGB('FFE2E2E2');

        // Add sample data
        $sampleData = [
            1, '3201012345678901', 'A-01', '3201011234567890', 'Contoh Nama', 'Laki-laki', 'Jakarta', '1990-05-15',
            'Islam', 'S1', 'Pegawai Swasta', 'Kawin', 'Kepala Keluarga', 'WNI', 'Nama Ayah', 'Nama Ibu',
            '081234567890', 'Jl. Contoh No. 123', '001', '002', 'Kelurahan Contoh', 'Kecamatan Contoh',
            'Kota Contoh', '12345'
        ];

        $col = 'A';
        foreach ($sampleData as $value) {
            $sheet->setCellValue($col . '2', $value);
            $col++;
        }

        // Auto size columns
        foreach (range('A', 'X') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);

        $filename = 'Template_Data_Kependudukan.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
}
