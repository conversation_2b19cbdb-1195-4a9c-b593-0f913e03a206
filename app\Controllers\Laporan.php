<?php

namespace App\Controllers;

use App\Models\PendapatanModel;
use App\Models\PendapatanLainnyaModel;
use App\Models\PengeluaranModel;
use App\Models\KategoriPengeluaranModel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Laporan extends BaseController
{
    protected $pendapatanModel;
    protected $pendapatanLainnyaModel;
    protected $pengeluaranModel;
    protected $kategoriPengeluaranModel;
    protected $settingsModel;

    public function __construct()
    {
        $this->pendapatanModel = new PendapatanModel();
        $this->pendapatanLainnyaModel = new PendapatanLainnyaModel();
        $this->pengeluaranModel = new PengeluaranModel();
        $this->kategoriPengeluaranModel = new KategoriPengeluaranModel();
        $this->settingsModel = new \App\Models\SettingsModel();
    }

    private function getProfilRT()
    {
        return $this->settingsModel->getProfilRTSettings();
    }

    public function index()
    {
        $bulan = $this->request->getGet('bulan');
        $tahun = $this->request->getGet('tahun') ?: date('Y');
        $jenis_laporan = $this->request->getGet('jenis') ?: 'bulanan';

        // Get data pendapatan
        $pendapatan_iuran_warga = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_warga', $bulan, $tahun);
        $pendapatan_iuran_sampah = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_sampah', $bulan, $tahun);
        $pendapatan_jimpitan = $this->pendapatanModel->getTotalPendapatanByJenis('jimpitan', $bulan, $tahun);
        
        if ($bulan) {
            $pendapatan_lainnya = $this->pendapatanLainnyaModel->getTotalPendapatanBulanan($bulan, $tahun);
        } else {
            $pendapatan_lainnya = $this->pendapatanLainnyaModel->where('YEAR(tanggal)', $tahun)->selectSum('jumlah', 'total')->first()['total'] ?? 0;
        }

        $total_pendapatan = $pendapatan_iuran_warga + $pendapatan_iuran_sampah + $pendapatan_jimpitan + $pendapatan_lainnya;

        // Get data pengeluaran per kategori
        $pengeluaran_per_kategori = $this->pengeluaranModel->getStatistikPengeluaranPerKategori($bulan, $tahun);
        $total_pengeluaran = array_sum(array_column($pengeluaran_per_kategori, 'total'));

        // Calculate saldo
        $saldo = $total_pendapatan - $total_pengeluaran;

        // Get chart data
        $chart_data = $this->getChartData($jenis_laporan, $bulan, $tahun);

        // Get detailed data for tables
        $detail_pendapatan = $this->getDetailPendapatan($bulan, $tahun);
        $detail_pengeluaran = $this->getDetailPengeluaran($bulan, $tahun);

        // Get data tunggakan KK yang wajib iuran
        $tunggakan_data = $this->getTunggakanKKWajibIuran($bulan, $tahun);

        // Get pagination parameters
        $page = (int)($this->request->getGet('page') ?? 1);
        $perPage = 5; // 5 items per page for better display

        // Get paginated tunggakan data
        $paginatedTunggakan = $this->getDetailTunggakanKKWajibIuranWithPagination($bulan, $tahun, $page, $perPage);
        $all_tunggakan_data = $this->getDetailTunggakanKKWajibIuran($bulan, $tahun);

        $detail_tunggakan_data = $paginatedTunggakan['data'];
        $pagination = $paginatedTunggakan['pagination'];

        // Get total tunggakan keseluruhan (tidak hanya per halaman)
        $total_tunggakan_keseluruhan = array_sum(array_column($all_tunggakan_data, 'total_tunggakan'));

        $data = [
            'title' => 'Laporan Keuangan - SMART',
            'bulan' => $bulan,
            'tahun' => $tahun,
            'jenis_laporan' => $jenis_laporan,
            'pendapatan_iuran_warga' => $pendapatan_iuran_warga,
            'pendapatan_iuran_sampah' => $pendapatan_iuran_sampah,
            'pendapatan_jimpitan' => $pendapatan_jimpitan,
            'pendapatan_lainnya' => $pendapatan_lainnya,
            'total_pendapatan' => $total_pendapatan,
            'pengeluaran_per_kategori' => $pengeluaran_per_kategori,
            'total_pengeluaran' => $total_pengeluaran,
            'saldo' => $saldo,
            'chart_data' => $chart_data,
            'detail_pendapatan' => $detail_pendapatan,
            'detail_pengeluaran' => $detail_pengeluaran,
            'tunggakan_data' => $tunggakan_data,
            'detail_tunggakan_data' => $detail_tunggakan_data,
            'pagination' => $pagination,
            'total_tunggakan_keseluruhan' => $total_tunggakan_keseluruhan,
            'pendapatanModel' => $this->pendapatanModel,
            'pengeluaranModel' => $this->pengeluaranModel
        ];

        return view('laporan/index', $data);
    }

    private function getChartData($jenis_laporan, $bulan, $tahun)
    {
        if ($jenis_laporan == 'bulanan' && $bulan) {
            // Data harian dalam bulan
            $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
            $chartData = [];
            
            for ($day = 1; $day <= $daysInMonth; $day++) {
                $date = sprintf('%04d-%02d-%02d', $tahun, $bulan, $day);
                
                // Get pendapatan
                $pendapatan_harian = $this->pendapatanModel->where('tanggal_bayar', $date)->selectSum('jumlah')->first()['jumlah'] ?? 0;
                $pendapatan_lainnya_harian = $this->pendapatanLainnyaModel->where('tanggal', $date)->selectSum('jumlah')->first()['jumlah'] ?? 0;
                $total_pendapatan_harian = $pendapatan_harian + $pendapatan_lainnya_harian;
                
                // Get pengeluaran
                $pengeluaran_harian = $this->pengeluaranModel->where('tanggal', $date)->selectSum('jumlah')->first()['jumlah'] ?? 0;
                
                $chartData[] = [
                    'label' => $day,
                    'pendapatan' => $total_pendapatan_harian,
                    'pengeluaran' => $pengeluaran_harian,
                    'saldo' => $total_pendapatan_harian - $pengeluaran_harian
                ];
            }
        } else {
            // Data bulanan dalam tahun
            $chartData = [];
            
            for ($month = 1; $month <= 12; $month++) {
                // Get pendapatan
                $pendapatan_iuran = $this->pendapatanModel->getTotalPendapatanByJenis('iuran_warga', $month, $tahun) +
                                   $this->pendapatanModel->getTotalPendapatanByJenis('iuran_sampah', $month, $tahun) +
                                   $this->pendapatanModel->getTotalPendapatanByJenis('jimpitan', $month, $tahun);
                $pendapatan_lainnya_bulanan = $this->pendapatanLainnyaModel->getTotalPendapatanBulanan($month, $tahun);
                $total_pendapatan_bulanan = $pendapatan_iuran + $pendapatan_lainnya_bulanan;
                
                // Get pengeluaran
                $pengeluaran_bulanan = $this->pengeluaranModel->getTotalPengeluaranBulanan($month, $tahun);
                
                $chartData[] = [
                    'label' => substr($this->pendapatanModel->getNamaBulan($month), 0, 3),
                    'pendapatan' => $total_pendapatan_bulanan,
                    'pengeluaran' => $pengeluaran_bulanan,
                    'saldo' => $total_pendapatan_bulanan - $pengeluaran_bulanan
                ];
            }
        }
        
        return $chartData;
    }

    private function getDetailPendapatan($bulan, $tahun)
    {
        $detail = [];
        
        // Iuran Warga
        $iuran_warga = $this->pendapatanModel->getPendapatanByJenis('iuran_warga', $bulan, $tahun);
        $detail['iuran_warga'] = [
            'nama' => 'Iuran Warga',
            'data' => $iuran_warga,
            'total' => array_sum(array_column($iuran_warga, 'jumlah'))
        ];
        
        // Iuran Sampah
        $iuran_sampah = $this->pendapatanModel->getPendapatanByJenis('iuran_sampah', $bulan, $tahun);
        $detail['iuran_sampah'] = [
            'nama' => 'Iuran Sampah',
            'data' => $iuran_sampah,
            'total' => array_sum(array_column($iuran_sampah, 'jumlah'))
        ];
        
        // Jimpitan
        $jimpitan = $this->pendapatanModel->getPendapatanByJenis('jimpitan', $bulan, $tahun);
        $detail['jimpitan'] = [
            'nama' => 'Jimpitan',
            'data' => $jimpitan,
            'total' => array_sum(array_column($jimpitan, 'jumlah'))
        ];
        
        // Pendapatan Lainnya
        if ($bulan) {
            $pendapatan_lainnya = $this->pendapatanLainnyaModel->getPendapatanBulanan($bulan, $tahun);
        } else {
            $pendapatan_lainnya = $this->pendapatanLainnyaModel->where('YEAR(tanggal)', $tahun)->orderBy('tanggal', 'DESC')->findAll();
        }
        $detail['pendapatan_lainnya'] = [
            'nama' => 'Pendapatan Lainnya',
            'data' => $pendapatan_lainnya,
            'total' => array_sum(array_column($pendapatan_lainnya, 'jumlah'))
        ];
        
        return $detail;
    }

    private function getDetailPengeluaran($bulan, $tahun)
    {
        $detail = [];
        $categories = $this->kategoriPengeluaranModel->getActiveCategories();
        
        foreach ($categories as $category) {
            $pengeluaran = $this->pengeluaranModel->getPengeluaranByKategori($category['id'], $bulan, $tahun);
            $detail[$category['id']] = [
                'nama' => $category['nama_kategori'],
                'warna' => $category['warna'],
                'data' => $pengeluaran,
                'total' => array_sum(array_column($pengeluaran, 'jumlah'))
            ];
        }
        
        return $detail;
    }

    public function exportExcel()
    {
        $bulan = $this->request->getGet('bulan');
        $tahun = $this->request->getGet('tahun') ?: date('Y');
        
        // Load PhpSpreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('SMART - Sistem Manajemen RT')
            ->setTitle('Laporan Keuangan RT')
            ->setSubject('Laporan Keuangan')
            ->setDescription('Laporan keuangan RT periode ' . ($bulan ? $this->pendapatanModel->getNamaBulan($bulan) . ' ' : '') . $tahun);
        
        // Header
        $sheet->setCellValue('A1', 'LAPORAN KEUANGAN RT');
        $sheet->setCellValue('A2', 'Periode: ' . ($bulan ? $this->pendapatanModel->getNamaBulan($bulan) . ' ' : 'Tahun ') . $tahun);
        $sheet->setCellValue('A3', 'Tanggal Cetak: ' . date('d/m/Y H:i:s'));
        
        // Get data
        $detail_pendapatan = $this->getDetailPendapatan($bulan, $tahun);
        $detail_pengeluaran = $this->getDetailPengeluaran($bulan, $tahun);
        
        $row = 5;
        
        // PENDAPATAN
        $sheet->setCellValue('A' . $row, 'PENDAPATAN');
        $row += 2;
        
        foreach ($detail_pendapatan as $key => $pendapatan) {
            $sheet->setCellValue('A' . $row, $pendapatan['nama']);
            $sheet->setCellValue('B' . $row, 'Rp ' . number_format($pendapatan['total'], 0, ',', '.'));
            $row++;
        }
        
        $total_pendapatan = array_sum(array_column($detail_pendapatan, 'total'));
        $sheet->setCellValue('A' . $row, 'TOTAL PENDAPATAN');
        $sheet->setCellValue('B' . $row, 'Rp ' . number_format($total_pendapatan, 0, ',', '.'));
        $row += 3;
        
        // PENGELUARAN
        $sheet->setCellValue('A' . $row, 'PENGELUARAN');
        $row += 2;
        
        foreach ($detail_pengeluaran as $pengeluaran) {
            if ($pengeluaran['total'] > 0) {
                $sheet->setCellValue('A' . $row, $pengeluaran['nama']);
                $sheet->setCellValue('B' . $row, 'Rp ' . number_format($pengeluaran['total'], 0, ',', '.'));
                $row++;
            }
        }
        
        $total_pengeluaran = array_sum(array_column($detail_pengeluaran, 'total'));
        $sheet->setCellValue('A' . $row, 'TOTAL PENGELUARAN');
        $sheet->setCellValue('B' . $row, 'Rp ' . number_format($total_pengeluaran, 0, ',', '.'));
        $row += 2;
        
        // SALDO
        $saldo = $total_pendapatan - $total_pengeluaran;
        $sheet->setCellValue('A' . $row, 'SALDO');
        $sheet->setCellValue('B' . $row, 'Rp ' . number_format($saldo, 0, ',', '.'));
        
        // Style the spreadsheet
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A2:A3')->getFont()->setSize(12);
        $sheet->getColumnDimension('A')->setWidth(30);
        $sheet->getColumnDimension('B')->setWidth(20);
        
        // Save file
        $filename = 'Laporan_Keuangan_' . ($bulan ? $this->pendapatanModel->getNamaBulan($bulan) . '_' : '') . $tahun . '.xlsx';
        
        $writer = new Xlsx($spreadsheet);
        
        // Set headers for download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit;
    }

    public function print()
    {
        $bulan = $this->request->getGet('bulan');
        $tahun = $this->request->getGet('tahun') ?: date('Y');
        
        // Get data
        $detail_pendapatan = $this->getDetailPendapatan($bulan, $tahun);
        $detail_pengeluaran = $this->getDetailPengeluaran($bulan, $tahun);
        
        $total_pendapatan = array_sum(array_column($detail_pendapatan, 'total'));
        $total_pengeluaran = array_sum(array_column($detail_pengeluaran, 'total'));
        $saldo = $total_pendapatan - $total_pengeluaran;
        
        $data = [
            'title' => 'Cetak Laporan Keuangan - SMART',
            'bulan' => $bulan,
            'tahun' => $tahun,
            'detail_pendapatan' => $detail_pendapatan,
            'detail_pengeluaran' => $detail_pengeluaran,
            'total_pendapatan' => $total_pendapatan,
            'total_pengeluaran' => $total_pengeluaran,
            'saldo' => $saldo,
            'pendapatanModel' => $this->pendapatanModel,
            'profil_rt' => $this->getProfilRT()
        ];
        
        return view('laporan/print', $data);
    }

    public function printTunggakan()
    {
        $bulan = $this->request->getGet('bulan');
        $tahun = $this->request->getGet('tahun') ?: date('Y');

        // Gunakan method yang hanya menampilkan KK wajib iuran
        $detail_tunggakan_data = $this->getDetailTunggakanKKWajibIuran($bulan, $tahun);
        $iuran_settings = $this->settingsModel->getIuranSettings();

        $data = [
            'title' => 'Laporan Tunggakan Iuran - SMART',
            'bulan' => $bulan,
            'tahun' => $tahun,
            'detail_tunggakan_data' => $detail_tunggakan_data,
            'iuran_settings' => $iuran_settings,
            'pendapatanModel' => $this->pendapatanModel,
            'profil_rt' => $this->getProfilRT()
        ];

        return view('laporan/print_tunggakan', $data);
    }

    private function getTunggakanIuran($bulan, $tahun)
    {
        // Jika tidak ada filter bulan, gunakan bulan saat ini
        $targetBulan = $bulan ?: date('n');
        $targetTahun = $tahun;

        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get daftar warga yang menunggak iuran warga
        $tunggakan_iuran_warga = $this->pendapatanModel->getKKBelumBayarIuran('iuran_warga', $targetBulan, $targetTahun);

        // Get daftar warga yang menunggak iuran sampah
        $tunggakan_iuran_sampah = $this->pendapatanModel->getKKBelumBayarIuran('iuran_sampah', $targetBulan, $targetTahun);

        // Gabungkan data dan hitung total tunggakan per warga
        $tunggakan_combined = [];

        // Proses tunggakan iuran warga
        foreach ($tunggakan_iuran_warga as $warga) {
            $no_kk = $warga['no_kk'];
            if (!isset($tunggakan_combined[$no_kk])) {
                $tunggakan_combined[$no_kk] = [
                    'no_kk' => $warga['no_kk'],
                    'nama_lengkap' => $warga['nama_lengkap'],
                    'no_telp' => $warga['no_telp'] ?? '-',
                    'tunggakan_iuran_warga' => true,
                    'tunggakan_iuran_sampah' => false,
                    'total_tunggakan' => 0
                ];
            }
            $tunggakan_combined[$no_kk]['tunggakan_iuran_warga'] = true;
            $tunggakan_combined[$no_kk]['total_tunggakan'] += $nominal_iuran_warga;
        }

        // Proses tunggakan iuran sampah
        foreach ($tunggakan_iuran_sampah as $warga) {
            $no_kk = $warga['no_kk'];
            if (!isset($tunggakan_combined[$no_kk])) {
                $tunggakan_combined[$no_kk] = [
                    'no_kk' => $warga['no_kk'],
                    'nama_lengkap' => $warga['nama_lengkap'],
                    'no_telp' => $warga['no_telp'] ?? '-',
                    'tunggakan_iuran_warga' => false,
                    'tunggakan_iuran_sampah' => true,
                    'total_tunggakan' => 0
                ];
            }
            $tunggakan_combined[$no_kk]['tunggakan_iuran_sampah'] = true;
            $tunggakan_combined[$no_kk]['total_tunggakan'] += $nominal_iuran_sampah;
        }

        // Convert ke array dan sort berdasarkan nama
        $result = array_values($tunggakan_combined);
        usort($result, function($a, $b) {
            return strcmp($a['nama_lengkap'], $b['nama_lengkap']);
        });

        return $result;
    }

    private function getTunggakanIuranPerRumah($bulan, $tahun)
    {
        // Jika tidak ada filter bulan, gunakan bulan saat ini
        $targetBulan = $bulan ?: date('n');
        $targetTahun = $tahun;

        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get daftar rumah dengan kepala keluarga
        $kependudukanModel = new \App\Models\KependudukanModel();
        $daftarRumah = $kependudukanModel->getDaftarRumahDenganKepalaKeluarga();

        $tunggakan_combined = [];

        foreach ($daftarRumah as $rumah) {
            $no_rumah = $rumah['no_rumah'];

            // Get semua KK dalam rumah ini
            $kkDalamRumah = $kependudukanModel->where('no_rumah', $no_rumah)
                                             ->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                             ->findAll();

            $total_tunggakan_rumah = 0;

            // Hitung total tunggakan untuk rumah ini (semua KK)
            foreach ($kkDalamRumah as $kk) {
                // Cek tunggakan iuran warga
                $bayarIuranWarga = $this->pendapatanModel
                    ->where('no_kk', $kk['no_kk'])
                    ->where('jenis_pendapatan', 'iuran_warga')
                    ->where('bulan', $targetBulan)
                    ->where('tahun', $targetTahun)
                    ->first();

                if (!$bayarIuranWarga) {
                    $total_tunggakan_rumah += $nominal_iuran_warga;
                }

                // Cek tunggakan iuran sampah
                $bayarIuranSampah = $this->pendapatanModel
                    ->where('no_kk', $kk['no_kk'])
                    ->where('jenis_pendapatan', 'iuran_sampah')
                    ->where('bulan', $targetBulan)
                    ->where('tahun', $targetTahun)
                    ->first();

                if (!$bayarIuranSampah) {
                    $total_tunggakan_rumah += $nominal_iuran_sampah;
                }
            }

            // Jika ada tunggakan, tambahkan ke hasil
            if ($total_tunggakan_rumah > 0) {
                // Hitung total tunggakan per jenis untuk rumah ini
                $total_warga_rumah = 0;
                $total_sampah_rumah = 0;
                $detail_tunggakan_warga = [];
                $detail_tunggakan_sampah = [];

                // Hitung ulang berdasarkan semua KK yang menunggak
                foreach ($kkDalamRumah as $kk) {
                    // Cek tunggakan iuran warga untuk KK ini
                    $bayarIuranWarga = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_warga')
                        ->where('bulan', $targetBulan)
                        ->where('tahun', $targetTahun)
                        ->first();

                    if (!$bayarIuranWarga) {
                        $total_warga_rumah += $nominal_iuran_warga;
                        $detail_tunggakan_warga[] = [
                            'bulan' => $targetBulan,
                            'tahun' => $targetTahun,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($targetBulan),
                            'nominal' => $nominal_iuran_warga,
                            'no_kk' => $kk['no_kk'],
                            'nama_kk' => $kk['nama_lengkap']
                        ];
                    }

                    // Cek tunggakan iuran sampah untuk KK ini
                    $bayarIuranSampah = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_sampah')
                        ->where('bulan', $targetBulan)
                        ->where('tahun', $targetTahun)
                        ->first();

                    if (!$bayarIuranSampah) {
                        $total_sampah_rumah += $nominal_iuran_sampah;
                        $detail_tunggakan_sampah[] = [
                            'bulan' => $targetBulan,
                            'tahun' => $targetTahun,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($targetBulan),
                            'nominal' => $nominal_iuran_sampah,
                            'no_kk' => $kk['no_kk'],
                            'nama_kk' => $kk['nama_lengkap']
                        ];
                    }
                }

                $tunggakan_combined[] = [
                    'no_rumah' => $no_rumah,
                    'no_kk' => $rumah['no_kk'], // KK utama untuk kontak
                    'nama_lengkap' => $rumah['nama_lengkap'], // Nama KK utama
                    'no_telp' => $rumah['no_telp'] ?? '-',
                    'tunggakan_iuran_warga' => $detail_tunggakan_warga,
                    'tunggakan_iuran_sampah' => $detail_tunggakan_sampah,
                    'total_tunggakan_warga' => $total_warga_rumah,
                    'total_tunggakan_sampah' => $total_sampah_rumah,
                    'total_tunggakan' => $total_warga_rumah + $total_sampah_rumah,
                    'jumlah_kk' => count($kkDalamRumah)
                ];
            }
        }

        // Sort berdasarkan no rumah
        usort($tunggakan_combined, function($a, $b) {
            return strcmp($a['no_rumah'], $b['no_rumah']);
        });

        return $tunggakan_combined;
    }

    /**
     * Get tunggakan KK yang wajib iuran
     */
    private function getTunggakanKKWajibIuran($bulan, $tahun)
    {
        // Jika tidak ada filter bulan, gunakan bulan saat ini
        $targetBulan = $bulan ?: date('n');
        $targetTahun = $tahun ?: date('Y');

        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get kepala keluarga yang wajib iuran
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluargaWajib = $kependudukanModel->getKepalaKeluargaWajibIuran();

        $result = [];
        foreach ($kepalaKeluargaWajib as $kk) {
            $total_tunggakan = 0;

            // Cek tunggakan iuran warga
            $bayarIuranWarga = $this->pendapatanModel
                ->where('no_kk', $kk['no_kk'])
                ->where('jenis_pendapatan', 'iuran_warga')
                ->where('bulan', $targetBulan)
                ->where('tahun', $targetTahun)
                ->first();

            if (!$bayarIuranWarga) {
                $total_tunggakan += $nominal_iuran_warga;
            }

            // Cek tunggakan iuran sampah
            $bayarIuranSampah = $this->pendapatanModel
                ->where('no_kk', $kk['no_kk'])
                ->where('jenis_pendapatan', 'iuran_sampah')
                ->where('bulan', $targetBulan)
                ->where('tahun', $targetTahun)
                ->first();

            if (!$bayarIuranSampah) {
                $total_tunggakan += $nominal_iuran_sampah;
            }

            // Jika ada tunggakan, masukkan ke hasil
            if ($total_tunggakan > 0) {
                $result[] = [
                    'no_kk' => $kk['no_kk'],
                    'nama_lengkap' => $kk['nama_lengkap'],
                    'no_telp' => $kk['no_telp'],
                    'total_tunggakan' => $total_tunggakan,
                    'tunggakan_iuran_warga' => !$bayarIuranWarga,
                    'tunggakan_iuran_sampah' => !$bayarIuranSampah
                ];
            }
        }

        return $result;
    }

    /**
     * Get detail tunggakan KK yang wajib iuran
     */
    private function getDetailTunggakanKKWajibIuran($bulan, $tahun)
    {
        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get periode settings
        $periode_settings = $this->settingsModel->getPeriodeSettings();
        $periode_mulai_bulan = $periode_settings['periode_mulai_bulan'];
        $periode_mulai_tahun = $periode_settings['periode_mulai_tahun'];

        // Get kepala keluarga yang wajib iuran
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluargaWajib = $kependudukanModel->getKepalaKeluargaWajibIuran();

        $result = [];
        $currentYear = date('Y');
        $currentMonth = date('n');

        // Tentukan range bulan yang akan dicek
        $startYear = $periode_mulai_tahun;
        $endYear = $currentYear;
        $startMonth = ($startYear == $periode_mulai_tahun) ? $periode_mulai_bulan : 1;
        $endMonth = $bulan ?: $currentMonth;

        foreach ($kepalaKeluargaWajib as $kk) {
            $tunggakanDetail = [
                'no_kk' => $kk['no_kk'],
                'nama_lengkap' => $kk['nama_lengkap'],
                'no_telp' => $kk['no_telp'],
                'tunggakan_iuran_warga' => [],
                'tunggakan_iuran_sampah' => [],
                'total_tunggakan_warga' => 0,
                'total_tunggakan_sampah' => 0,
                'total_tunggakan' => 0
            ];

            // Loop through months to check for unpaid dues
            for ($year = $startYear; $year <= $endYear; $year++) {
                $monthStart = ($year == $startYear) ? $startMonth : 1;
                $monthEnd = ($year == $endYear) ? $endMonth : 12;

                for ($month = $monthStart; $month <= $monthEnd; $month++) {
                    // Cek apakah sudah bayar iuran warga
                    $bayarIuranWarga = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_warga')
                        ->where('bulan', $month)
                        ->where('tahun', $year)
                        ->first();

                    if (!$bayarIuranWarga) {
                        $tunggakanDetail['tunggakan_iuran_warga'][] = [
                            'bulan' => $month,
                            'tahun' => $year,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                            'nominal' => $nominal_iuran_warga
                        ];
                        $tunggakanDetail['total_tunggakan_warga'] += $nominal_iuran_warga;
                    }

                    // Cek apakah sudah bayar iuran sampah
                    $bayarIuranSampah = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_sampah')
                        ->where('bulan', $month)
                        ->where('tahun', $year)
                        ->first();

                    if (!$bayarIuranSampah) {
                        $tunggakanDetail['tunggakan_iuran_sampah'][] = [
                            'bulan' => $month,
                            'tahun' => $year,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                            'nominal' => $nominal_iuran_sampah
                        ];
                        $tunggakanDetail['total_tunggakan_sampah'] += $nominal_iuran_sampah;
                    }
                }
            }

            // Hitung total tunggakan
            $tunggakanDetail['total_tunggakan'] = $tunggakanDetail['total_tunggakan_warga'] + $tunggakanDetail['total_tunggakan_sampah'];

            // Hanya masukkan jika ada tunggakan
            if ($tunggakanDetail['total_tunggakan'] > 0) {
                $result[] = $tunggakanDetail;
            }
        }

        return $result;
    }

    /**
     * Get detail tunggakan KK wajib iuran with pagination
     */
    private function getDetailTunggakanKKWajibIuranWithPagination($bulan, $tahun, $page = 1, $perPage = 10)
    {
        // Get all tunggakan data first
        $allTunggakan = $this->getDetailTunggakanKKWajibIuran($bulan, $tahun);

        // Calculate pagination
        $total = count($allTunggakan);
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;

        // Get data for current page
        $data = array_slice($allTunggakan, $offset, $perPage);

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_previous' => $page > 1,
                'has_next' => $page < $totalPages,
                'previous_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null
            ]
        ];
    }

    private function getDetailTunggakanPerKK($bulan, $tahun, $page = null, $perPage = 10)
    {
        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get periode mulai data dari settings
        $periode_settings = $this->settingsModel->getPeriodeSettings();
        $periode_mulai_bulan = $periode_settings['periode_mulai_bulan'];
        $periode_mulai_tahun = $periode_settings['periode_mulai_tahun'];

        // Get semua Kepala Keluarga
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluarga = $kependudukanModel->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                           ->orderBy('nama_lengkap', 'ASC')
                                           ->findAll();

        $result = [];
        $currentYear = date('Y');
        $currentMonth = date('n');

        // Tentukan range bulan yang akan dicek berdasarkan periode mulai
        $startYear = max($periode_mulai_tahun, $tahun ?: $periode_mulai_tahun);
        $endYear = $currentYear;
        $startMonth = ($startYear == $periode_mulai_tahun) ? $periode_mulai_bulan : 1;
        $endMonth = $bulan ?: $currentMonth;

        foreach ($kepalaKeluarga as $kk) {
            $tunggakanDetail = [
                'no_kk' => $kk['no_kk'],
                'nama_lengkap' => $kk['nama_lengkap'],
                'no_telp' => $kk['no_telp'] ?? '-',
                'tunggakan_iuran_warga' => [],
                'tunggakan_iuran_sampah' => [],
                'total_tunggakan_warga' => 0,
                'total_tunggakan_sampah' => 0,
                'total_tunggakan' => 0
            ];

            // Cek tunggakan untuk setiap bulan
            for ($year = $startYear; $year <= $endYear; $year++) {
                $monthStart = ($year == $startYear) ? $startMonth : 1;
                $monthEnd = ($year == $endYear) ? $endMonth : 12;

                for ($month = $monthStart; $month <= $monthEnd; $month++) {
                    // Skip bulan yang belum terjadi
                    if ($year == $currentYear && $month > $currentMonth) {
                        continue;
                    }

                    // Cek apakah sudah bayar iuran warga
                    $bayarIuranWarga = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_warga')
                        ->where('bulan', $month)
                        ->where('tahun', $year)
                        ->first();

                    if (!$bayarIuranWarga) {
                        $tunggakanDetail['tunggakan_iuran_warga'][] = [
                            'bulan' => $month,
                            'tahun' => $year,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                            'nominal' => $nominal_iuran_warga
                        ];
                        $tunggakanDetail['total_tunggakan_warga'] += $nominal_iuran_warga;
                    }

                    // Cek apakah sudah bayar iuran sampah
                    $bayarIuranSampah = $this->pendapatanModel
                        ->where('no_kk', $kk['no_kk'])
                        ->where('jenis_pendapatan', 'iuran_sampah')
                        ->where('bulan', $month)
                        ->where('tahun', $year)
                        ->first();

                    if (!$bayarIuranSampah) {
                        $tunggakanDetail['tunggakan_iuran_sampah'][] = [
                            'bulan' => $month,
                            'tahun' => $year,
                            'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                            'nominal' => $nominal_iuran_sampah
                        ];
                        $tunggakanDetail['total_tunggakan_sampah'] += $nominal_iuran_sampah;
                    }
                }
            }

            $tunggakanDetail['total_tunggakan'] = $tunggakanDetail['total_tunggakan_warga'] + $tunggakanDetail['total_tunggakan_sampah'];

            // Hanya tambahkan jika ada tunggakan
            if ($tunggakanDetail['total_tunggakan'] > 0) {
                $result[] = $tunggakanDetail;
            }
        }

        return $result;
    }

    private function getDetailTunggakanPerKKWithPagination($bulan, $tahun, $page = 1, $perPage = 10)
    {
        // Get all tunggakan data first
        $allTunggakan = $this->getDetailTunggakanPerKK($bulan, $tahun);

        // Calculate pagination
        $total = count($allTunggakan);
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;

        // Get paginated data
        $paginatedData = array_slice($allTunggakan, $offset, $perPage);

        return [
            'data' => $paginatedData,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_previous' => $page > 1,
                'has_next' => $page < $totalPages,
                'previous_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null
            ]
        ];
    }

    private function getDetailTunggakanPerRumah($bulan, $tahun)
    {
        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get periode settings
        $periode_settings = $this->settingsModel->getPeriodeSettings();
        $periode_mulai_bulan = $periode_settings['periode_mulai_bulan'];
        $periode_mulai_tahun = $periode_settings['periode_mulai_tahun'];

        // Get daftar rumah dengan kepala keluarga
        $kependudukanModel = new \App\Models\KependudukanModel();
        $daftarRumah = $kependudukanModel->getDaftarRumahDenganKepalaKeluarga();

        $result = [];
        $currentYear = date('Y');
        $currentMonth = date('n');

        // Tentukan range bulan yang akan dicek
        $startYear = $periode_mulai_tahun;
        $endYear = $currentYear;
        $startMonth = ($startYear == $periode_mulai_tahun) ? $periode_mulai_bulan : 1;
        $endMonth = $bulan ?: $currentMonth;

        foreach ($daftarRumah as $rumah) {
            $tunggakanDetail = [
                'no_rumah' => $rumah['no_rumah'],
                'no_kk' => $rumah['no_kk'], // KK utama untuk kontak
                'nama_lengkap' => $rumah['nama_lengkap'], // Nama KK utama
                'no_telp' => $rumah['no_telp'] ?? '-',
                'tunggakan_iuran_warga' => [],
                'tunggakan_iuran_sampah' => [],
                'total_tunggakan_warga' => 0,
                'total_tunggakan_sampah' => 0,
                'total_tunggakan' => 0,
                'jumlah_kk' => 0
            ];

            // Get semua KK dalam rumah ini
            $kkDalamRumah = $kependudukanModel->where('no_rumah', $rumah['no_rumah'])
                                             ->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                             ->findAll();

            $tunggakanDetail['jumlah_kk'] = count($kkDalamRumah);

            // Cek tunggakan untuk setiap bulan
            for ($year = $startYear; $year <= $endYear; $year++) {
                $monthStart = ($year == $startYear) ? $startMonth : 1;
                $monthEnd = ($year == $endYear) ? $endMonth : 12;

                for ($month = $monthStart; $month <= $monthEnd; $month++) {
                    // Cek tunggakan untuk setiap KK dalam rumah
                    foreach ($kkDalamRumah as $kk) {
                        // Cek apakah sudah bayar iuran warga
                        $bayarIuranWarga = $this->pendapatanModel
                            ->where('no_kk', $kk['no_kk'])
                            ->where('jenis_pendapatan', 'iuran_warga')
                            ->where('bulan', $month)
                            ->where('tahun', $year)
                            ->first();

                        if (!$bayarIuranWarga) {
                            $tunggakanDetail['tunggakan_iuran_warga'][] = [
                                'bulan' => $month,
                                'tahun' => $year,
                                'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                                'nominal' => $nominal_iuran_warga,
                                'no_kk' => $kk['no_kk'],
                                'nama_kk' => $kk['nama_lengkap']
                            ];
                            $tunggakanDetail['total_tunggakan_warga'] += $nominal_iuran_warga;
                        }

                        // Cek apakah sudah bayar iuran sampah
                        $bayarIuranSampah = $this->pendapatanModel
                            ->where('no_kk', $kk['no_kk'])
                            ->where('jenis_pendapatan', 'iuran_sampah')
                            ->where('bulan', $month)
                            ->where('tahun', $year)
                            ->first();

                        if (!$bayarIuranSampah) {
                            $tunggakanDetail['tunggakan_iuran_sampah'][] = [
                                'bulan' => $month,
                                'tahun' => $year,
                                'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                                'nominal' => $nominal_iuran_sampah,
                                'no_kk' => $kk['no_kk'],
                                'nama_kk' => $kk['nama_lengkap']
                            ];
                            $tunggakanDetail['total_tunggakan_sampah'] += $nominal_iuran_sampah;
                        }
                    }
                }
            }

            $tunggakanDetail['total_tunggakan'] = $tunggakanDetail['total_tunggakan_warga'] + $tunggakanDetail['total_tunggakan_sampah'];

            // Hanya tambahkan jika ada tunggakan
            if ($tunggakanDetail['total_tunggakan'] > 0) {
                $result[] = $tunggakanDetail;
            }
        }

        return $result;
    }

    private function getDetailTunggakanPerRumahWithPagination($bulan, $tahun, $page = 1, $perPage = 10)
    {
        // Get all tunggakan data first
        $allTunggakan = $this->getDetailTunggakanPerRumah($bulan, $tahun);

        // Calculate pagination
        $total = count($allTunggakan);
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;

        // Get paginated data
        $paginatedData = array_slice($allTunggakan, $offset, $perPage);

        return [
            'data' => $paginatedData,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_previous' => $page > 1,
                'has_next' => $page < $totalPages,
                'previous_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null
            ]
        ];
    }

    public function detailTunggakan($no_kk)
    {
        // Get detail warga
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluarga = $kependudukanModel->where('no_kk', $no_kk)
                                           ->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                           ->first();

        if (!$kepalaKeluarga) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data Kepala Keluarga tidak ditemukan');
        }

        // Get anggota keluarga
        $anggotaKeluarga = $kependudukanModel->where('no_kk', $no_kk)
                                            ->orderBy('status_hubungan_keluarga', 'ASC')
                                            ->findAll();

        // Get detail tunggakan untuk KK ini
        $detailTunggakan = $this->getDetailTunggakanSingleKK($no_kk);

        $data = [
            'title' => 'Detail Tunggakan - ' . $kepalaKeluarga['nama_lengkap'],
            'kepala_keluarga' => $kepalaKeluarga,
            'anggota_keluarga' => $anggotaKeluarga,
            'detail_tunggakan' => $detailTunggakan,
            'pendapatanModel' => $this->pendapatanModel
        ];

        return view('laporan/detail_tunggakan', $data);
    }

    private function getDetailTunggakanSingleKK($no_kk)
    {
        // Get nominal iuran dari settings
        $nominal_iuran_warga = $this->settingsModel->getSetting('iuran_warga_nominal', 50000);
        $nominal_iuran_sampah = $this->settingsModel->getSetting('iuran_sampah_nominal', 15000);

        // Get periode mulai data dari settings
        $periode_settings = $this->settingsModel->getPeriodeSettings();
        $periode_mulai_bulan = $periode_settings['periode_mulai_bulan'];
        $periode_mulai_tahun = $periode_settings['periode_mulai_tahun'];

        $currentYear = date('Y');
        $currentMonth = date('n');

        $tunggakanDetail = [
            'tunggakan_iuran_warga' => [],
            'tunggakan_iuran_sampah' => [],
            'riwayat_pembayaran_warga' => [],
            'riwayat_pembayaran_sampah' => [],
            'total_tunggakan_warga' => 0,
            'total_tunggakan_sampah' => 0,
            'total_tunggakan' => 0
        ];

        // Cek tunggakan dan riwayat pembayaran berdasarkan periode mulai
        $startMonth = ($currentYear == $periode_mulai_tahun) ? $periode_mulai_bulan : 1;
        $endMonth = $currentMonth;

        // Jika tahun saat ini kurang dari periode mulai, tidak ada tunggakan
        if ($currentYear < $periode_mulai_tahun) {
            return $tunggakanDetail;
        }

        for ($month = $startMonth; $month <= $endMonth; $month++) {
            // Cek pembayaran iuran warga
            $bayarIuranWarga = $this->pendapatanModel
                ->where('no_kk', $no_kk)
                ->where('jenis_pendapatan', 'iuran_warga')
                ->where('bulan', $month)
                ->where('tahun', $currentYear)
                ->first();

            if ($bayarIuranWarga) {
                $tunggakanDetail['riwayat_pembayaran_warga'][] = [
                    'bulan' => $month,
                    'tahun' => $currentYear,
                    'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                    'tanggal_bayar' => $bayarIuranWarga['tanggal_bayar'],
                    'jumlah' => $bayarIuranWarga['jumlah'],
                    'keterangan' => $bayarIuranWarga['keterangan']
                ];
            } else {
                $tunggakanDetail['tunggakan_iuran_warga'][] = [
                    'bulan' => $month,
                    'tahun' => $currentYear,
                    'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                    'nominal' => $nominal_iuran_warga
                ];
                $tunggakanDetail['total_tunggakan_warga'] += $nominal_iuran_warga;
            }

            // Cek pembayaran iuran sampah
            $bayarIuranSampah = $this->pendapatanModel
                ->where('no_kk', $no_kk)
                ->where('jenis_pendapatan', 'iuran_sampah')
                ->where('bulan', $month)
                ->where('tahun', $currentYear)
                ->first();

            if ($bayarIuranSampah) {
                $tunggakanDetail['riwayat_pembayaran_sampah'][] = [
                    'bulan' => $month,
                    'tahun' => $currentYear,
                    'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                    'tanggal_bayar' => $bayarIuranSampah['tanggal_bayar'],
                    'jumlah' => $bayarIuranSampah['jumlah'],
                    'keterangan' => $bayarIuranSampah['keterangan']
                ];
            } else {
                $tunggakanDetail['tunggakan_iuran_sampah'][] = [
                    'bulan' => $month,
                    'tahun' => $currentYear,
                    'bulan_nama' => $this->pendapatanModel->getNamaBulan($month),
                    'nominal' => $nominal_iuran_sampah
                ];
                $tunggakanDetail['total_tunggakan_sampah'] += $nominal_iuran_sampah;
            }
        }

        $tunggakanDetail['total_tunggakan'] = $tunggakanDetail['total_tunggakan_warga'] + $tunggakanDetail['total_tunggakan_sampah'];

        return $tunggakanDetail;
    }

    public function printDetailTunggakan($no_kk)
    {
        // Get detail warga
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluarga = $kependudukanModel->where('no_kk', $no_kk)
                                           ->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                           ->first();

        if (!$kepalaKeluarga) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data Kepala Keluarga tidak ditemukan');
        }

        // Get anggota keluarga
        $anggotaKeluarga = $kependudukanModel->where('no_kk', $no_kk)
                                            ->orderBy('status_hubungan_keluarga', 'ASC')
                                            ->findAll();

        // Get detail tunggakan untuk KK ini
        $detailTunggakan = $this->getDetailTunggakanSingleKK($no_kk);
        $iuran_settings = $this->settingsModel->getIuranSettings();

        $data = [
            'title' => 'Detail Tunggakan - ' . $kepalaKeluarga['nama_lengkap'],
            'kepala_keluarga' => $kepalaKeluarga,
            'anggota_keluarga' => $anggotaKeluarga,
            'detail_tunggakan' => $detailTunggakan,
            'iuran_settings' => $iuran_settings,
            'pendapatanModel' => $this->pendapatanModel,
            'profil_rt' => $this->getProfilRT()
        ];

        return view('laporan/print_detail_tunggakan', $data);
    }

    /**
     * Bayar iuran dari laporan tunggakan
     */
    public function bayarIuran()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $no_kk = $this->request->getPost('no_kk');
        $jenis_pendapatan = $this->request->getPost('jenis_pendapatan');
        $bulan = $this->request->getPost('bulan');
        $tahun = $this->request->getPost('tahun');
        $jumlah = $this->request->getPost('jumlah');
        $tanggal_bayar = $this->request->getPost('tanggal_bayar') ?: date('Y-m-d');

        // Validasi input
        if (!$no_kk || !$jenis_pendapatan || !$bulan || !$tahun || !$jumlah) {
            return $this->response->setJSON(['success' => false, 'message' => 'Data tidak lengkap']);
        }

        // Cek apakah sudah bayar
        $pendapatanModel = new \App\Models\PendapatanModel();
        $sudahBayar = $pendapatanModel->sudahBayarIuran($no_kk, $jenis_pendapatan, $bulan, $tahun);

        if ($sudahBayar) {
            $namaJenis = $pendapatanModel->getNamaJenisPendapatan($jenis_pendapatan);
            $namaBulan = $pendapatanModel->getNamaBulan($bulan);
            return $this->response->setJSON([
                'success' => false,
                'message' => "KK {$no_kk} sudah membayar {$namaJenis} untuk bulan {$namaBulan} {$tahun}!"
            ]);
        }

        // Get nama pembayar dari KK
        $kependudukanModel = new \App\Models\KependudukanModel();
        $kepalaKeluarga = $kependudukanModel->where('no_kk', $no_kk)
                                           ->where('status_hubungan_keluarga', 'Kepala Keluarga')
                                           ->first();

        if (!$kepalaKeluarga) {
            return $this->response->setJSON(['success' => false, 'message' => 'Data kepala keluarga tidak ditemukan']);
        }

        // Get nama admin yang sedang login
        $userModel = new \App\Models\UserModel();
        $adminUser = $userModel->find(session()->get('user_id'));
        $namaAdmin = $adminUser['username'];

        // Simpan pembayaran
        $data = [
            'jenis_pendapatan' => $jenis_pendapatan,
            'no_kk' => $no_kk,
            'nama_pembayar' => $kepalaKeluarga['nama_lengkap'],
            'jumlah' => $jumlah,
            'bulan' => $bulan,
            'tahun' => $tahun,
            'tanggal_bayar' => $tanggal_bayar,
            'keterangan' => 'Pembayaran diterima oleh ' . $namaAdmin,
            'created_by' => session()->get('user_id')
        ];

        if ($pendapatanModel->insert($data)) {
            $namaJenis = $pendapatanModel->getNamaJenisPendapatan($jenis_pendapatan);
            $namaBulan = $pendapatanModel->getNamaBulan($bulan);
            return $this->response->setJSON([
                'success' => true,
                'message' => "Pembayaran {$namaJenis} bulan {$namaBulan} {$tahun} untuk KK {$no_kk} berhasil disimpan!"
            ]);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Gagal menyimpan pembayaran']);
        }
    }
}
