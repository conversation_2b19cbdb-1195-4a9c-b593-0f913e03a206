<?php

namespace App\Controllers;

use App\Models\KegiatanModel;

class Kegiatan extends BaseController
{
    protected $kegiatanModel;

    public function __construct()
    {
        $this->kegiatanModel = new KegiatanModel();
    }

    public function index()
    {
        $search = $this->request->getGet('search');
        $kategori = $this->request->getGet('kategori');
        $bulan = $this->request->getGet('bulan');
        $perPage = 12;

        // Check if user is admin
        $isAdmin = session()->get('role') === 'admin';
        
        if ($isAdmin) {
            $status = $this->request->getGet('status');
            $data_kegiatan = $this->kegiatanModel->getAllKegiatanWithFilter($search, $kategori, $status, $bulan, 15);
            $statistics = $this->kegiatanModel->getStatistics();
        } else {
            $data_kegiatan = $this->kegiatanModel->getKegiatanWithFilter($search, $kategori, $bulan, $perPage);
            $statistics = null;
        }
        
        $pager = $this->kegiatanModel->pager;
        $available_categories = $this->kegiatanModel->getAvailableCategories();

        $data = [
            'title' => 'Kegiatan & Dokumentasi - SMART',
            'data_kegiatan' => $data_kegiatan,
            'pager' => $pager,
            'statistics' => $statistics,
            'available_categories' => $available_categories,
            'search' => $search,
            'kategori' => $kategori,
            'bulan' => $bulan,
            'status' => $isAdmin ? $this->request->getGet('status') : null,
            'isAdmin' => $isAdmin,
            'kegiatanModel' => $this->kegiatanModel
        ];

        return view('kegiatan/index', $data);
    }

    public function show($slug)
    {
        $kegiatan = $this->kegiatanModel->getBySlug($slug);
        
        if (!$kegiatan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Kegiatan tidak ditemukan');
        }

        // Increment views
        $this->kegiatanModel->incrementViews($kegiatan['id']);

        // Get related kegiatan
        $related = $this->kegiatanModel->where('kategori', $kegiatan['kategori'])
                                     ->where('id !=', $kegiatan['id'])
                                     ->where('is_published', 1)
                                     ->orderBy('tanggal_kegiatan', 'DESC')
                                     ->limit(3)
                                     ->findAll();

        $data = [
            'title' => $kegiatan['judul'] . ' - SMART',
            'kegiatan' => $kegiatan,
            'related' => $related,
            'kegiatanModel' => $this->kegiatanModel
        ];

        return view('kegiatan/show', $data);
    }

    public function create()
    {
        // Only admin can create
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/kegiatan')->with('error', 'Akses ditolak!');
        }

        $available_categories = $this->kegiatanModel->getAvailableCategories();
        
        $data = [
            'title' => 'Tambah Kegiatan - SMART',
            'available_categories' => $available_categories
        ];

        return view('kegiatan/create', $data);
    }

    public function store()
    {
        // Only admin can store
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/kegiatan')->with('error', 'Akses ditolak!');
        }

        $rules = [
            'judul' => 'required|min_length[5]|max_length[200]',
            'konten' => 'required|min_length[10]',
            'tanggal_kegiatan' => 'required|valid_date',
            'kategori' => 'required|in_list[Kegiatan,Berita,Dokumentasi,Pengumuman]',
            'foto' => 'if_exist|uploaded[foto]|max_size[foto,2048]|is_image[foto]|mime_in[foto,image/jpg,image/jpeg,image/png]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate slug
        $slug = $this->kegiatanModel->generateSlug($this->request->getPost('judul'));

        // Handle file upload
        $foto = null;
        $file = $this->request->getFile('foto');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Create directory if not exists
            $uploadPath = FCPATH . 'uploads/kegiatan';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $newName = $file->getRandomName();
            $file->move($uploadPath, $newName);
            $foto = $newName;
        }

        $data = [
            'judul' => $this->request->getPost('judul'),
            'slug' => $slug,
            'konten' => $this->request->getPost('konten'),
            'foto' => $foto,
            'tanggal_kegiatan' => $this->request->getPost('tanggal_kegiatan'),
            'lokasi' => $this->request->getPost('lokasi'),
            'kategori' => $this->request->getPost('kategori'),
            'is_published' => $this->request->getPost('is_published') ? 1 : 0,
            'created_by' => session()->get('user_id')
        ];

        if ($this->kegiatanModel->save($data)) {
            return redirect()->to('/kegiatan')->with('success', 'Kegiatan berhasil ditambahkan!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan kegiatan!');
        }
    }

    public function edit($id)
    {
        // Only admin can edit
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/kegiatan')->with('error', 'Akses ditolak!');
        }

        $kegiatan = $this->kegiatanModel->find($id);
        
        if (!$kegiatan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Kegiatan tidak ditemukan');
        }

        $available_categories = $this->kegiatanModel->getAvailableCategories();

        $data = [
            'title' => 'Edit Kegiatan - SMART',
            'kegiatan' => $kegiatan,
            'available_categories' => $available_categories
        ];

        return view('kegiatan/edit', $data);
    }

    public function update($id)
    {
        // Only admin can update
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/kegiatan')->with('error', 'Akses ditolak!');
        }

        $kegiatan = $this->kegiatanModel->find($id);

        if (!$kegiatan) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Kegiatan tidak ditemukan');
        }

        $rules = [
            'judul' => 'required|min_length[5]|max_length[200]',
            'konten' => 'required|min_length[10]',
            'tanggal_kegiatan' => 'required|valid_date',
            'kategori' => 'required|in_list[Kegiatan,Berita,Dokumentasi,Pengumuman]',
            'foto' => 'if_exist|uploaded[foto]|max_size[foto,2048]|is_image[foto]|mime_in[foto,image/jpg,image/jpeg,image/png]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate slug if title changed
        $slug = $kegiatan['slug'];
        if ($this->request->getPost('judul') !== $kegiatan['judul']) {
            $slug = $this->kegiatanModel->generateSlug($this->request->getPost('judul'), $id);
        }

        // Handle file upload
        $foto = $kegiatan['foto'];

        // Check if user wants to delete current photo
        if ($this->request->getPost('hapus_foto')) {
            if ($foto && file_exists(FCPATH . 'uploads/kegiatan/' . $foto)) {
                unlink(FCPATH . 'uploads/kegiatan/' . $foto);
            }
            $foto = null;
        }

        $file = $this->request->getFile('foto');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Create directory if not exists
            $uploadPath = FCPATH . 'uploads/kegiatan';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old file if uploading new one
            if ($foto && file_exists($uploadPath . '/' . $foto)) {
                unlink($uploadPath . '/' . $foto);
            }

            $newName = $file->getRandomName();
            $file->move($uploadPath, $newName);
            $foto = $newName;
        }

        $data = [
            'judul' => $this->request->getPost('judul'),
            'slug' => $slug,
            'konten' => $this->request->getPost('konten'),
            'foto' => $foto,
            'tanggal_kegiatan' => $this->request->getPost('tanggal_kegiatan'),
            'lokasi' => $this->request->getPost('lokasi'),
            'kategori' => $this->request->getPost('kategori'),
            'is_published' => $this->request->getPost('is_published') ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            if ($this->kegiatanModel->update($id, $data)) {
                return redirect()->to('/kegiatan')->with('success', 'Kegiatan berhasil diperbarui!');
            } else {
                $errors = $this->kegiatanModel->errors();
                $errorMessage = !empty($errors) ? implode(', ', $errors) : 'Gagal memperbarui kegiatan!';
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        // Only admin can delete
        if (session()->get('role') !== 'admin') {
            return redirect()->to('/kegiatan')->with('error', 'Akses ditolak!');
        }

        $kegiatan = $this->kegiatanModel->find($id);
        
        if (!$kegiatan) {
            return redirect()->to('/kegiatan')->with('error', 'Kegiatan tidak ditemukan!');
        }

        // Delete photo file
        if ($kegiatan['foto'] && file_exists(FCPATH . 'uploads/kegiatan/' . $kegiatan['foto'])) {
            unlink(FCPATH . 'uploads/kegiatan/' . $kegiatan['foto']);
        }

        if ($this->kegiatanModel->delete($id)) {
            return redirect()->to('/kegiatan')->with('success', 'Kegiatan berhasil dihapus!');
        } else {
            return redirect()->to('/kegiatan')->with('error', 'Gagal menghapus kegiatan!');
        }
    }

    public function togglePublish($id)
    {
        // Only admin can toggle publish
        if (session()->get('role') !== 'admin') {
            return $this->response->setJSON(['success' => false, 'message' => 'Akses ditolak!']);
        }

        if ($this->kegiatanModel->togglePublish($id)) {
            return $this->response->setJSON(['success' => true, 'message' => 'Status publikasi berhasil diubah!']);
        } else {
            return $this->response->setJSON(['success' => false, 'message' => 'Gagal mengubah status publikasi!']);
        }
    }

    public function downloadFoto($filename)
    {
        $filepath = WRITEPATH . 'uploads/kegiatan/' . $filename;
        
        if (file_exists($filepath)) {
            return $this->response->download($filepath, null);
        } else {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('File tidak ditemukan');
        }
    }
}
