<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\AutoTrackingSender;

class TrackingAutoSend extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Tracking';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'tracking:auto';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Manage auto-send tracking system';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'tracking:auto [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--status'     => 'Show auto-send status and statistics',
        '--force-send' => 'Force send data now (bypass interval)',
        '--test'       => 'Test auto-send system',
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $autoSender = new AutoTrackingSender();

        // Show status
        if (CLI::getOption('status')) {
            $this->showAutoSendStatus($autoSender);
            return;
        }

        // Force send
        if (CLI::getOption('force-send')) {
            $this->forceSendData($autoSender);
            return;
        }

        // Test system
        if (CLI::getOption('test')) {
            $this->testAutoSendSystem($autoSender);
            return;
        }

        // Default: show status
        $this->showAutoSendStatus($autoSender);
    }

    /**
     * Show auto-send status and statistics
     *
     * @param AutoTrackingSender $autoSender
     * @return void
     */
    protected function showAutoSendStatus(AutoTrackingSender $autoSender): void
    {
        CLI::write('=== Auto-Send Tracking Status ===', 'yellow');
        CLI::newLine();

        $stats = $autoSender->getAutoSendStats();

        // System status
        CLI::write('System Status:', 'cyan');
        CLI::write('  Auto-send enabled: ' . ($stats['auto_send_enabled'] ? 'YES' : 'NO'), 
                  $stats['auto_send_enabled'] ? 'green' : 'red');
        CLI::write('  Send interval: ' . $stats['send_interval_minutes'] . ' minutes');
        CLI::write('  Pending records: ' . $stats['pending_records'], 
                  $stats['pending_records'] > 0 ? 'yellow' : 'green');

        CLI::newLine();

        // Timing information
        CLI::write('Timing Information:', 'cyan');
        CLI::write('  Last send: ' . $stats['last_send_time']);
        
        if ($stats['next_send_due']) {
            CLI::write('  Next send due: ' . $stats['next_send_due']);
            CLI::write('  Minutes until next: ' . $stats['minutes_until_next_send']);
        } else {
            CLI::write('  Next send: When data is available');
        }

        CLI::write('  Should send now: ' . ($stats['should_send_now'] ? 'YES' : 'NO'),
                  $stats['should_send_now'] ? 'green' : 'yellow');

        CLI::newLine();

        // Recommendations
        if ($stats['pending_records'] > 0 && $stats['should_send_now']) {
            CLI::write('💡 Recommendation: Data is ready to be sent!', 'green');
            CLI::write('   Run: php spark tracking:auto --force-send');
        } elseif ($stats['pending_records'] > 0) {
            CLI::write('💡 Recommendation: Data will be sent automatically in ' . 
                      $stats['minutes_until_next_send'] . ' minutes', 'yellow');
        } else {
            CLI::write('✅ All data is up to date!', 'green');
        }
    }

    /**
     * Force send data now
     *
     * @param AutoTrackingSender $autoSender
     * @return void
     */
    protected function forceSendData(AutoTrackingSender $autoSender): void
    {
        CLI::write('Force sending tracking data...', 'yellow');
        CLI::newLine();

        $result = $autoSender->forceSend();

        if ($result['success']) {
            CLI::write('✅ Success!', 'green');
            CLI::write('  Records sent: ' . $result['sent']);
            
            if (isset($result['failed']) && $result['failed'] > 0) {
                CLI::write('  Records failed: ' . $result['failed'], 'red');
            }
        } else {
            CLI::write('❌ Failed to send data', 'red');
            
            if (!empty($result['errors'])) {
                CLI::write('Errors:', 'red');
                foreach ($result['errors'] as $error) {
                    CLI::write('  - ' . $error, 'red');
                }
            }
        }
    }

    /**
     * Test auto-send system
     *
     * @param AutoTrackingSender $autoSender
     * @return void
     */
    protected function testAutoSendSystem(AutoTrackingSender $autoSender): void
    {
        CLI::write('=== Testing Auto-Send System ===', 'yellow');
        CLI::newLine();

        // Test 1: Check configuration
        CLI::write('1. Testing Configuration...', 'cyan');
        $stats = $autoSender->getAutoSendStats();
        
        if ($stats['auto_send_enabled']) {
            CLI::write('   ✅ Auto-send is enabled', 'green');
        } else {
            CLI::write('   ❌ Auto-send is disabled', 'red');
            CLI::write('   💡 Enable in Config/Tracking.php or .env', 'yellow');
        }

        CLI::write('   Interval: ' . $stats['send_interval_minutes'] . ' minutes');
        CLI::newLine();

        // Test 2: Check pending data
        CLI::write('2. Testing Data Availability...', 'cyan');
        
        if ($stats['pending_records'] > 0) {
            CLI::write('   ✅ Found ' . $stats['pending_records'] . ' pending records', 'green');
        } else {
            CLI::write('   ⚠️  No pending data to send', 'yellow');
            CLI::write('   💡 Browse the application to generate tracking data', 'yellow');
        }
        CLI::newLine();

        // Test 3: Test send logic
        CLI::write('3. Testing Send Logic...', 'cyan');
        
        if ($stats['should_send_now']) {
            CLI::write('   ✅ System indicates data should be sent now', 'green');
            
            // Perform actual test send
            CLI::write('   Testing actual send...', 'yellow');
            $result = $autoSender->forceSend();
            
            if ($result['success']) {
                CLI::write('   ✅ Test send successful!', 'green');
                CLI::write('   Records sent: ' . $result['sent']);
            } else {
                CLI::write('   ❌ Test send failed', 'red');
                if (!empty($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        CLI::write('   Error: ' . $error, 'red');
                    }
                }
            }
        } else {
            CLI::write('   ⚠️  System indicates no send needed at this time', 'yellow');
            CLI::write('   Last send: ' . $stats['last_send_time']);
            CLI::write('   Next due: ' . ($stats['next_send_due'] ?? 'When data available'));
        }

        CLI::newLine();

        // Test 4: Overall assessment
        CLI::write('4. Overall Assessment:', 'cyan');
        
        $issues = [];
        if (!$stats['auto_send_enabled']) $issues[] = 'Auto-send disabled';
        if ($stats['pending_records'] == 0) $issues[] = 'No test data available';
        
        if (empty($issues)) {
            CLI::write('   ✅ Auto-send system is working correctly!', 'green');
            CLI::write('   💡 Data will be sent automatically during normal app usage', 'green');
        } else {
            CLI::write('   ⚠️  Issues found:', 'yellow');
            foreach ($issues as $issue) {
                CLI::write('   - ' . $issue, 'yellow');
            }
        }
    }
}
