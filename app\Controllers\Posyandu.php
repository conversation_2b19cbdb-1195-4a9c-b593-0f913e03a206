<?php

namespace App\Controllers;

use App\Models\BalitaModel;
use App\Models\RemajaModel;
use App\Models\LansiaModel;
use App\Models\PosbinduModel;
use App\Models\MonthlyRecordModel;
use App\Models\BumilModel;
use App\Models\BusuiModel;
use App\Models\SettingsModel;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

class Posyandu extends BaseController
{
    protected $balitaModel;
    protected $remajaModel;
    protected $lansiaModel;
    protected $posbinduModel;
    protected $monthlyRecordModel;
    protected $bumilModel;
    protected $busuiModel;
    protected $bumilMonthlyRecordModel;
    protected $busuiMonthlyRecordModel;
    protected $settingsModel;


    public function __construct()
    {
        $this->balitaModel = new BalitaModel();
        $this->remajaModel = new RemajaModel();
        $this->lansiaModel = new LansiaModel();
        $this->posbinduModel = new PosbinduModel();
        $this->monthlyRecordModel = new MonthlyRecordModel();
        $this->bumilModel = new BumilModel();
        $this->busuiModel = new BusuiModel();
        $this->bumilMonthlyRecordModel = new \App\Models\BumilMonthlyRecordModel();
        $this->busuiMonthlyRecordModel = new \App\Models\BusuiMonthlyRecordModel();
        $this->settingsModel = new SettingsModel();

    }

    public function index()
    {
        $tab = $this->request->getGet('tab') ?? 'balita';
        $search = $this->request->getGet('search');
        $perPage = 15;

        $data = [
            'title' => 'Posyandu - SMART',
            'tab' => $tab,
            'search' => $search
        ];

        // Load data based on active tab
        switch ($tab) {
            case 'balita':
                $data['data_balita'] = $this->getBalitaWithLatestRecords($search, $perPage);
                $data['pager'] = $this->balitaModel->pager;
                $data['statistics'] = $this->balitaModel->getStatistics();
                break;
            case 'remaja':
                $data['data_remaja'] = $this->getRemajaWithLatestRecords($search, $perPage);
                $data['pager'] = $this->remajaModel->pager;
                $data['statistics'] = $this->remajaModel->getStatistics();
                break;
            case 'lansia':
                $data['data_lansia'] = $this->getLansiaWithLatestRecords($search, $perPage);
                $data['pager'] = $this->lansiaModel->pager;
                $data['statistics'] = $this->lansiaModel->getStatistics();
                break;
            case 'posbindu':
                $data['data_posbindu'] = $this->getPosbinduWithLatestRecords($search, $perPage);
                $data['pager'] = $this->posbinduModel->pager;
                $data['statistics'] = $this->posbinduModel->getStatistics();
                break;
            case 'bumil':
                $data['data_bumil'] = $this->bumilModel->getBumilWithFilter($search, $perPage);
                $data['pager'] = $this->bumilModel->pager;
                $data['statistics'] = $this->bumilModel->getStatistics();
                break;
            case 'busui':
                $data['data_busui'] = $this->busuiModel->getBusuiWithFilter($search, $perPage);
                $data['pager'] = $this->busuiModel->pager;
                $data['statistics'] = $this->busuiModel->getStatistics();
                break;
        }

        return view('posyandu/index', $data);
    }

    // Balita CRUD methods
    public function createBalita()
    {
        $data = [
            'title' => 'Tambah Data Balita - SMART',
            'type' => 'balita'
        ];
        return view('posyandu/form', $data);
    }

    public function storeBalita()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_months($data['tanggal_lahir']);
        }

        if ($this->balitaModel->save($data)) {
            session()->setFlashdata('success', 'Data balita berhasil ditambahkan');
            return redirect()->to('/posyandu?tab=balita');
        } else {
            session()->setFlashdata('errors', $this->balitaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function editBalita($id)
    {
        $balita = $this->balitaModel->find($id);
        if (!$balita) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data balita tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Balita - SMART',
            'type' => 'balita',
            'data' => $balita
        ];
        return view('posyandu/form', $data);
    }

    public function updateBalita($id)
    {
        $data = $this->request->getPost();

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_months($data['tanggal_lahir']);
        }

        if ($this->balitaModel->update($id, $data)) {
            session()->setFlashdata('success', 'Data balita berhasil diperbarui');
            return redirect()->to('/posyandu?tab=balita');
        } else {
            session()->setFlashdata('errors', $this->balitaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function deleteBalita($id)
    {
        if ($this->balitaModel->delete($id)) {
            session()->setFlashdata('success', 'Data balita berhasil dihapus');
        } else {
            session()->setFlashdata('error', 'Gagal menghapus data balita');
        }
        return redirect()->to('/posyandu?tab=balita');
    }

    // Remaja CRUD methods
    public function createRemaja()
    {
        $data = [
            'title' => 'Tambah Data Remaja - SMART',
            'type' => 'remaja'
        ];
        return view('posyandu/form', $data);
    }

    public function storeRemaja()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->remajaModel->save($data)) {
            session()->setFlashdata('success', 'Data remaja berhasil ditambahkan');
            return redirect()->to('/posyandu?tab=remaja');
        } else {
            session()->setFlashdata('errors', $this->remajaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function editRemaja($id)
    {
        $remaja = $this->remajaModel->find($id);
        if (!$remaja) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data remaja tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Remaja - SMART',
            'type' => 'remaja',
            'data' => $remaja
        ];
        return view('posyandu/form', $data);
    }

    public function updateRemaja($id)
    {
        $data = $this->request->getPost();

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->remajaModel->update($id, $data)) {
            session()->setFlashdata('success', 'Data remaja berhasil diperbarui');
            return redirect()->to('/posyandu?tab=remaja');
        } else {
            session()->setFlashdata('errors', $this->remajaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function deleteRemaja($id)
    {
        if ($this->remajaModel->delete($id)) {
            session()->setFlashdata('success', 'Data remaja berhasil dihapus');
        } else {
            session()->setFlashdata('error', 'Gagal menghapus data remaja');
        }
        return redirect()->to('/posyandu?tab=remaja');
    }

    // Lansia CRUD methods
    public function createLansia()
    {
        $data = [
            'title' => 'Tambah Data Lansia - SMART',
            'type' => 'lansia'
        ];
        return view('posyandu/form', $data);
    }

    public function storeLansia()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->lansiaModel->save($data)) {
            session()->setFlashdata('success', 'Data lansia berhasil ditambahkan');
            return redirect()->to('/posyandu?tab=lansia');
        } else {
            session()->setFlashdata('errors', $this->lansiaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function editLansia($id)
    {
        $lansia = $this->lansiaModel->find($id);
        if (!$lansia) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data lansia tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Lansia - SMART',
            'type' => 'lansia',
            'data' => $lansia
        ];
        return view('posyandu/form', $data);
    }

    public function updateLansia($id)
    {
        $data = $this->request->getPost();

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->lansiaModel->update($id, $data)) {
            session()->setFlashdata('success', 'Data lansia berhasil diperbarui');
            return redirect()->to('/posyandu?tab=lansia');
        } else {
            session()->setFlashdata('errors', $this->lansiaModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function deleteLansia($id)
    {
        if ($this->lansiaModel->delete($id)) {
            session()->setFlashdata('success', 'Data lansia berhasil dihapus');
        } else {
            session()->setFlashdata('error', 'Gagal menghapus data lansia');
        }
        return redirect()->to('/posyandu?tab=lansia');
    }

    // Posbindu CRUD methods
    public function createPosbindu()
    {
        $data = [
            'title' => 'Tambah Data Posbindu - SMART',
            'type' => 'posbindu'
        ];
        return view('posyandu/form', $data);
    }

    public function storePosbindu()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->posbinduModel->save($data)) {
            session()->setFlashdata('success', 'Data posbindu berhasil ditambahkan');
            return redirect()->to('/posyandu?tab=posbindu');
        } else {
            session()->setFlashdata('errors', $this->posbinduModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function editPosbindu($id)
    {
        $posbindu = $this->posbinduModel->find($id);
        if (!$posbindu) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data posbindu tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Posbindu - SMART',
            'type' => 'posbindu',
            'data' => $posbindu
        ];
        return view('posyandu/form', $data);
    }

    public function updatePosbindu($id)
    {
        $data = $this->request->getPost();

        // Calculate age automatically from birth date
        if (!empty($data['tanggal_lahir'])) {
            $data['umur'] = get_age_in_years($data['tanggal_lahir']);
        }

        if ($this->posbinduModel->update($id, $data)) {
            session()->setFlashdata('success', 'Data posbindu berhasil diperbarui');
            return redirect()->to('/posyandu?tab=posbindu');
        } else {
            session()->setFlashdata('errors', $this->posbinduModel->errors());
            return redirect()->back()->withInput();
        }
    }

    public function deletePosbindu($id)
    {
        if ($this->posbinduModel->delete($id)) {
            session()->setFlashdata('success', 'Data posbindu berhasil dihapus');
        } else {
            session()->setFlashdata('error', 'Gagal menghapus data posbindu');
        }
        return redirect()->to('/posyandu?tab=posbindu');
    }

    // Detail view for monthly records
    public function detail($id)
    {
        $type = $this->request->getGet('type');

        if (!in_array($type, ['balita', 'remaja', 'lansia', 'posbindu'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tipe tidak valid');
        }

        // Get person data based on type
        $person = null;
        switch ($type) {
            case 'balita':
                $person = $this->balitaModel->find($id);
                break;
            case 'remaja':
                $person = $this->remajaModel->find($id);
                break;
            case 'lansia':
                $person = $this->lansiaModel->find($id);
                break;
            case 'posbindu':
                $person = $this->posbinduModel->find($id);
                break;
        }

        if (!$person) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data tidak ditemukan');
        }

        // Get monthly records
        $monthlyRecords = $this->monthlyRecordModel->getRecordsByPerson($id, $type);
        $statistics = $this->monthlyRecordModel->getMonthlyStatistics($id, $type);

        $data = [
            'title' => 'Detail Perkembangan - SMART',
            'person' => $person,
            'type' => $type,
            'monthly_records' => $monthlyRecords,
            'statistics' => $statistics
        ];

        return view('posyandu/detail', $data);
    }

    public function storeMonthlyRecord()
    {
        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');

        if ($this->monthlyRecordModel->save($data)) {
            session()->setFlashdata('success', 'Data bulanan berhasil ditambahkan');
        } else {
            session()->setFlashdata('errors', $this->monthlyRecordModel->errors());
        }

        return redirect()->to('/posyandu/detail/' . $data['person_id'] . '?type=' . $data['person_type']);
    }

    public function editMonthlyRecord($id)
    {
        $record = $this->monthlyRecordModel->find($id);
        if (!$record) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data tidak ditemukan');
        }

        // Get person data
        $person = null;
        switch ($record['person_type']) {
            case 'balita':
                $person = $this->balitaModel->find($record['person_id']);
                break;
            case 'remaja':
                $person = $this->remajaModel->find($record['person_id']);
                break;
            case 'lansia':
                $person = $this->lansiaModel->find($record['person_id']);
                break;
            case 'posbindu':
                $person = $this->posbinduModel->find($record['person_id']);
                break;
        }

        $data = [
            'title' => 'Edit Data Bulanan - SMART',
            'record' => $record,
            'person' => $person,
            'type' => $record['person_type']
        ];

        return view('posyandu/monthly_form', $data);
    }

    public function updateMonthlyRecord($id)
    {
        $data = $this->request->getPost();

        if ($this->monthlyRecordModel->update($id, $data)) {
            session()->setFlashdata('success', 'Data bulanan berhasil diperbarui');
        } else {
            session()->setFlashdata('errors', $this->monthlyRecordModel->errors());
        }

        $record = $this->monthlyRecordModel->find($id);
        return redirect()->to('/posyandu/detail/' . $record['person_id'] . '?type=' . $record['person_type']);
    }

    public function deleteMonthlyRecord($id)
    {
        $record = $this->monthlyRecordModel->find($id);
        if (!$record) {
            session()->setFlashdata('error', 'Data tidak ditemukan');
            return redirect()->back();
        }

        if ($this->monthlyRecordModel->delete($id)) {
            session()->setFlashdata('success', 'Data bulanan berhasil dihapus');
        } else {
            session()->setFlashdata('error', 'Gagal menghapus data bulanan');
        }

        return redirect()->to('/posyandu/detail/' . $record['person_id'] . '?type=' . $record['person_type']);
    }

    // Helper methods to get data with latest records
    private function getBalitaWithLatestRecords($search = null, $perPage = 15)
    {
        $balitaData = $this->balitaModel->getBalitaWithFilter($search, $perPage);

        // Add latest record data to each balita
        foreach ($balitaData as &$balita) {
            $latestRecord = $this->monthlyRecordModel->getLatestRecord($balita['id'], 'balita');
            if ($latestRecord) {
                $balita['berat_badan'] = $latestRecord['berat_badan'];
                $balita['tinggi_badan'] = $latestRecord['tinggi_badan'];
                $balita['lingkar_lengan'] = $latestRecord['lingkar_lengan'];
                $balita['lingkar_kepala'] = $latestRecord['lingkar_kepala'];
                $balita['keterangan'] = $latestRecord['keterangan'];
                $balita['updated_at'] = $latestRecord['updated_at'];
            } else {
                // If no monthly record, use null values
                $balita['berat_badan'] = null;
                $balita['tinggi_badan'] = null;
                $balita['lingkar_lengan'] = null;
                $balita['lingkar_kepala'] = null;
                $balita['keterangan'] = null;
            }
        }

        return $balitaData;
    }

    private function getRemajaWithLatestRecords($search = null, $perPage = 15)
    {
        $remajaData = $this->remajaModel->getRemajaWithFilter($search, $perPage);

        // Add latest record data to each remaja
        foreach ($remajaData as &$remaja) {
            $latestRecord = $this->monthlyRecordModel->getLatestRecord($remaja['id'], 'remaja');
            if ($latestRecord) {
                $remaja['berat_badan'] = $latestRecord['berat_badan'];
                $remaja['tinggi_badan'] = $latestRecord['tinggi_badan'];
                $remaja['lingkar_lengan'] = $latestRecord['lingkar_lengan'];
                $remaja['lingkar_perut'] = $latestRecord['lingkar_perut'];
                $remaja['tensi'] = $latestRecord['tensi'];
                $remaja['keterangan'] = $latestRecord['keterangan'];
                $remaja['updated_at'] = $latestRecord['updated_at'];
            } else {
                // If no monthly record, use null values
                $remaja['berat_badan'] = null;
                $remaja['tinggi_badan'] = null;
                $remaja['lingkar_lengan'] = null;
                $remaja['lingkar_perut'] = null;
                $remaja['tensi'] = null;
                $remaja['keterangan'] = null;
            }
        }

        return $remajaData;
    }

    private function getLansiaWithLatestRecords($search = null, $perPage = 15)
    {
        $lansiaData = $this->lansiaModel->getLansiaWithFilter($search, $perPage);

        // Add latest record data to each lansia
        foreach ($lansiaData as &$lansia) {
            $latestRecord = $this->monthlyRecordModel->getLatestRecord($lansia['id'], 'lansia');
            if ($latestRecord) {
                $lansia['berat_badan'] = $latestRecord['berat_badan'];
                $lansia['tinggi_badan'] = $latestRecord['tinggi_badan'];
                $lansia['lingkar_lengan'] = $latestRecord['lingkar_lengan'];
                $lansia['lingkar_perut'] = $latestRecord['lingkar_perut'];
                $lansia['tensi'] = $latestRecord['tensi'];
                $lansia['kolesterol'] = $latestRecord['kolesterol'];
                $lansia['gula_darah'] = $latestRecord['gula_darah'];
                $lansia['asam_urat'] = $latestRecord['asam_urat'];
                $lansia['keterangan'] = $latestRecord['keterangan'];
                $lansia['updated_at'] = $latestRecord['updated_at'];
            } else {
                // If no monthly record, use null values
                $lansia['berat_badan'] = null;
                $lansia['tinggi_badan'] = null;
                $lansia['lingkar_lengan'] = null;
                $lansia['lingkar_perut'] = null;
                $lansia['tensi'] = null;
                $lansia['kolesterol'] = null;
                $lansia['gula_darah'] = null;
                $lansia['asam_urat'] = null;
                $lansia['keterangan'] = null;
            }
        }

        return $lansiaData;
    }

    private function getPosbinduWithLatestRecords($search = null, $perPage = 15)
    {
        $posbinduData = $this->posbinduModel->getPosbinduWithFilter($search, $perPage);

        // Add latest record data to each posbindu
        foreach ($posbinduData as &$posbindu) {
            $latestRecord = $this->monthlyRecordModel->getLatestRecord($posbindu['id'], 'posbindu');
            if ($latestRecord) {
                $posbindu['berat_badan'] = $latestRecord['berat_badan'];
                $posbindu['tinggi_badan'] = $latestRecord['tinggi_badan'];
                $posbindu['lingkar_lengan'] = $latestRecord['lingkar_lengan'];
                $posbindu['lingkar_perut'] = $latestRecord['lingkar_perut'];
                $posbindu['tensi'] = $latestRecord['tensi'];
                $posbindu['kolesterol'] = $latestRecord['kolesterol'];
                $posbindu['gula_darah'] = $latestRecord['gula_darah'];
                $posbindu['asam_urat'] = $latestRecord['asam_urat'];
                $posbindu['keterangan'] = $latestRecord['keterangan'];
                $posbindu['updated_at'] = $latestRecord['updated_at'];
            } else {
                // If no monthly record, use null values
                $posbindu['berat_badan'] = null;
                $posbindu['tinggi_badan'] = null;
                $posbindu['lingkar_lengan'] = null;
                $posbindu['lingkar_perut'] = null;
                $posbindu['tensi'] = null;
                $posbindu['kolesterol'] = null;
                $posbindu['gula_darah'] = null;
                $posbindu['asam_urat'] = null;
                $posbindu['keterangan'] = null;
            }
        }

        return $posbinduData;
    }

    // Export to Excel with health data
    public function exportExcel($type)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('SMART - Sistem Manajemen RT')
            ->setTitle('Export Data ' . ucfirst($type))
            ->setSubject('Data Posyandu')
            ->setDescription('Export data ' . $type . ' lengkap dengan data kesehatan');

        // Get data with health information
        switch ($type) {
            case 'balita':
                $data = $this->getBalitaWithHealthData();
                $headers = [
                    'A1' => 'NIK',
                    'B1' => 'Nama Balita',
                    'C1' => 'Tanggal Lahir',
                    'D1' => 'Jenis Kelamin',
                    'E1' => 'Nama Ibu',
                    'F1' => 'Umur',
                    'G1' => 'BB (KG)',
                    'H1' => 'TB (CM)',
                    'I1' => 'LL (CM)',
                    'J1' => 'LK (CM)',
                    'K1' => 'Keterangan',
                    'L1' => 'Tanggal Update',
                    'M1' => 'Tanggal Dibuat'
                ];
                break;

            case 'remaja':
                $data = $this->getRemajaWithHealthData();
                $headers = [
                    'A1' => 'NIK',
                    'B1' => 'Nama Lengkap',
                    'C1' => 'Tanggal Lahir',
                    'D1' => 'Jenis Kelamin',
                    'E1' => 'Umur',
                    'F1' => 'BB (KG)',
                    'G1' => 'TB (CM)',
                    'H1' => 'Keterangan',
                    'I1' => 'Tanggal Update',
                    'J1' => 'Tanggal Dibuat'
                ];
                break;

            case 'lansia':
                $data = $this->getLansiaWithHealthData();
                $headers = [
                    'A1' => 'NIK',
                    'B1' => 'Nama Lengkap',
                    'C1' => 'Tanggal Lahir',
                    'D1' => 'Jenis Kelamin',
                    'E1' => 'Umur',
                    'F1' => 'BB (KG)',
                    'G1' => 'TB (CM)',
                    'H1' => 'Keterangan',
                    'I1' => 'Tanggal Update',
                    'J1' => 'Tanggal Dibuat'
                ];
                break;

            case 'posbindu':
                $data = $this->getPosbinduWithHealthData();
                $headers = [
                    'A1' => 'NIK',
                    'B1' => 'Nama Lengkap',
                    'C1' => 'Tanggal Lahir',
                    'D1' => 'Jenis Kelamin',
                    'E1' => 'Umur',
                    'F1' => 'BB (KG)',
                    'G1' => 'TB (CM)',
                    'H1' => 'Tensi',
                    'I1' => 'Kolesterol',
                    'J1' => 'Gula Darah',
                    'K1' => 'Asam Urat',
                    'L1' => 'Keterangan',
                    'M1' => 'Tanggal Update',
                    'N1' => 'Tanggal Dibuat'
                ];
                break;

            case 'bumil':
                $data = $this->bumilModel->orderBy('nama_ibu', 'ASC')->findAll();
                $headers = [
                    'A1' => 'Nama Ibu',
                    'B1' => 'NIK Ibu',
                    'C1' => 'Nama Suami',
                    'D1' => 'No. KK',
                    'E1' => 'Usia Kehamilan (minggu)',
                    'F1' => 'Status',
                    'G1' => 'Keterangan'
                ];
                break;

            case 'busui':
                $data = $this->busuiModel->orderBy('nama_ibu', 'ASC')->findAll();
                $headers = [
                    'A1' => 'Nama Ibu',
                    'B1' => 'NIK Ibu',
                    'C1' => 'Nama Anak',
                    'D1' => 'Tanggal Lahir Anak',
                    'E1' => 'Jenis Kelamin',
                    'F1' => 'Status',
                    'G1' => 'Keterangan'
                ];
                break;
        }

        // Set headers
        foreach ($headers as $cell => $header) {
            $sheet->setCellValue($cell, $header);
        }

        // Fill data
        $row = 2;
        foreach ($data as $item) {
            switch ($type) {
                case 'balita':
                    $sheet->setCellValue('A' . $row, $item['nik']);
                    $sheet->setCellValue('B' . $row, $item['nama_balita']);
                    $sheet->setCellValue('C' . $row, $item['tanggal_lahir']);
                    $sheet->setCellValue('D' . $row, $item['jenis_kelamin']);
                    $sheet->setCellValue('E' . $row, $item['nama_ibu']);
                    $sheet->setCellValue('F' . $row, $item['umur']);
                    $sheet->setCellValue('G' . $row, $item['berat_badan'] ?? '-');
                    $sheet->setCellValue('H' . $row, $item['tinggi_badan'] ?? '-');
                    $sheet->setCellValue('I' . $row, $item['lingkar_lengan'] ?? '-');
                    $sheet->setCellValue('J' . $row, $item['lingkar_kepala'] ?? '-');
                    $sheet->setCellValue('K' . $row, $item['keterangan'] ?? '-');
                    $sheet->setCellValue('L' . $row, $item['updated_at'] ?? '-');
                    $sheet->setCellValue('M' . $row, $item['created_at']);
                    break;

                case 'remaja':
                case 'lansia':
                    $sheet->setCellValue('A' . $row, $item['nik']);
                    $sheet->setCellValue('B' . $row, $item['nama_lengkap']);
                    $sheet->setCellValue('C' . $row, $item['tanggal_lahir']);
                    $sheet->setCellValue('D' . $row, $item['jenis_kelamin']);
                    $sheet->setCellValue('E' . $row, $item['umur']);
                    $sheet->setCellValue('F' . $row, $item['berat_badan'] ?? '-');
                    $sheet->setCellValue('G' . $row, $item['tinggi_badan'] ?? '-');
                    $sheet->setCellValue('H' . $row, $item['keterangan'] ?? '-');
                    $sheet->setCellValue('I' . $row, $item['updated_at'] ?? '-');
                    $sheet->setCellValue('J' . $row, $item['created_at']);
                    break;

                case 'posbindu':
                    $sheet->setCellValue('A' . $row, $item['nik']);
                    $sheet->setCellValue('B' . $row, $item['nama_lengkap']);
                    $sheet->setCellValue('C' . $row, $item['tanggal_lahir']);
                    $sheet->setCellValue('D' . $row, $item['jenis_kelamin']);
                    $sheet->setCellValue('E' . $row, $item['umur']);
                    $sheet->setCellValue('F' . $row, $item['berat_badan'] ?? '-');
                    $sheet->setCellValue('G' . $row, $item['tinggi_badan'] ?? '-');
                    $sheet->setCellValue('H' . $row, $item['tensi'] ?? '-');
                    $sheet->setCellValue('I' . $row, $item['kolesterol'] ?? '-');
                    $sheet->setCellValue('J' . $row, $item['gula_darah'] ?? '-');
                    $sheet->setCellValue('K' . $row, $item['asam_urat'] ?? '-');
                    $sheet->setCellValue('L' . $row, $item['keterangan'] ?? '-');
                    $sheet->setCellValue('M' . $row, $item['updated_at'] ?? '-');
                    $sheet->setCellValue('N' . $row, $item['created_at']);
                    break;

                case 'senam':
                    $sheet->setCellValue('A' . $row, $item['nama_lengkap']);
                    $sheet->setCellValue('B' . $row, $item['nik'] ?? '-');
                    $sheet->setCellValue('C' . $row, format_tanggal_indonesia($item['tanggal_daftar'], 'short'));
                    $sheet->setCellValue('D' . $row, ucfirst($item['status_aktif']));
                    $sheet->setCellValue('E' . $row, $item['keterangan'] ?? '-');
                    $sheet->setCellValue('F' . $row, format_tanggal_indonesia($item['created_at'], 'short'));
                    $sheet->setCellValue('G' . $row, format_tanggal_indonesia($item['updated_at'], 'short'));
                    break;

                case 'toga':
                    $sheet->setCellValue('A' . $row, $item['nama_kepala_keluarga']);
                    $sheet->setCellValue('B' . $row, $item['no_kk'] ?? '-');
                    $sheet->setCellValue('C' . $row, format_tanggal_indonesia($item['tanggal_daftar'], 'short'));
                    $sheet->setCellValue('D' . $row, ucfirst($item['status_aktif']));
                    $sheet->setCellValue('E' . $row, $item['keterangan'] ?? '-');
                    $sheet->setCellValue('F' . $row, format_tanggal_indonesia($item['created_at'], 'short'));
                    $sheet->setCellValue('G' . $row, format_tanggal_indonesia($item['updated_at'], 'short'));
                    break;

                case 'bumil':
                    $sheet->setCellValue('A' . $row, $item['nama_ibu']);
                    $sheet->setCellValue('B' . $row, $item['nik_ibu'] ?? '-');
                    $sheet->setCellValue('C' . $row, $item['nama_suami'] ?? '-');
                    $sheet->setCellValue('D' . $row, $item['no_kk'] ?? '-');
                    $sheet->setCellValue('E' . $row, $item['usia_kehamilan_minggu'] ?? '-');
                    $sheet->setCellValue('F' . $row, ucfirst(str_replace('_', ' ', $item['status_aktif'])));
                    $sheet->setCellValue('G' . $row, $item['keterangan'] ?? '-');
                    break;

                case 'busui':
                    $sheet->setCellValue('A' . $row, $item['nama_ibu']);
                    $sheet->setCellValue('B' . $row, $item['nik_ibu'] ?? '-');
                    $sheet->setCellValue('C' . $row, $item['nama_anak']);
                    $sheet->setCellValue('D' . $row, $item['tanggal_lahir_anak']);
                    $sheet->setCellValue('E' . $row, $item['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan');
                    $sheet->setCellValue('F' . $row, ucfirst(str_replace('_', ' ', $item['status_aktif'])));
                    $sheet->setCellValue('G' . $row, $item['keterangan'] ?? '-');
                    break;
            }
            $row++;
        }

        // Style the header
        $lastColumn = '';
        switch ($type) {
            case 'balita':
                $lastColumn = 'M';
                break;
            case 'remaja':
            case 'lansia':
                $lastColumn = 'J';
                break;
            case 'posbindu':
                $lastColumn = 'N';
                break;
            case 'senam':
            case 'toga':
            case 'bumil':
            case 'busui':
                $lastColumn = 'G';
                break;
        }

        $headerRange = 'A1:' . $lastColumn . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle($headerRange)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
        $sheet->getStyle($headerRange)->getFill()->getStartColor()->setARGB('FF4472C4');
        $sheet->getStyle($headerRange)->getFont()->getColor()->setARGB('FFFFFFFF');
        $sheet->getStyle($headerRange)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle($headerRange)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

        // Auto-size columns
        foreach (range('A', $lastColumn) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Add borders to data
        if ($row > 2) {
            $dataRange = 'A2:' . $lastColumn . ($row - 1);
            $sheet->getStyle($dataRange)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        }

        $filename = 'Data_' . ucfirst($type) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        $writer = new Xlsx($spreadsheet);

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    // Import from Excel
    public function importExcel($type)
    {
        $file = $this->request->getFile('excel_file');

        if (!$file->isValid()) {
            session()->setFlashdata('error', 'File tidak valid');
            return redirect()->back();
        }

        if (!in_array($file->getExtension(), ['xlsx', 'xls'])) {
            session()->setFlashdata('error', 'File harus berformat Excel (.xlsx atau .xls)');
            return redirect()->back();
        }

        try {
            $spreadsheet = IOFactory::load($file->getTempName());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Skip header row
            array_shift($rows);

            $successCount = 0;
            $errorCount = 0;

            foreach ($rows as $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    $data = [];

                    switch ($type) {
                        case 'balita':
                            $data = [
                                'nik' => $row[0] ?? '',
                                'nama_balita' => $row[1] ?? '',
                                'tanggal_lahir' => $row[2] ?? '',
                                'jenis_kelamin' => strtolower($row[3] ?? ''),
                                'nama_ibu' => $row[4] ?? '',
                                'created_by' => session()->get('user_id')
                            ];

                            // Calculate age
                            if (!empty($data['tanggal_lahir'])) {
                                $data['umur'] = get_age_in_years($data['tanggal_lahir']);
                            }

                            $this->balitaModel->save($data);
                            break;

                        case 'remaja':
                            $data = [
                                'nik' => $row[0] ?? '',
                                'nama_lengkap' => $row[1] ?? '',
                                'tanggal_lahir' => $row[2] ?? '',
                                'jenis_kelamin' => strtolower($row[3] ?? ''),
                                'created_by' => session()->get('user_id')
                            ];

                            // Calculate age
                            if (!empty($data['tanggal_lahir'])) {
                                $data['umur'] = get_age_in_years($data['tanggal_lahir']);
                            }

                            $this->remajaModel->save($data);
                            break;

                        case 'lansia':
                            $data = [
                                'nik' => $row[0] ?? '',
                                'nama_lengkap' => $row[1] ?? '',
                                'tanggal_lahir' => $row[2] ?? '',
                                'jenis_kelamin' => strtolower($row[3] ?? ''),
                                'created_by' => session()->get('user_id')
                            ];

                            // Calculate age
                            if (!empty($data['tanggal_lahir'])) {
                                $data['umur'] = get_age_in_years($data['tanggal_lahir']);
                            }

                            $this->lansiaModel->save($data);
                            break;

                        case 'posbindu':
                            $data = [
                                'nik' => $row[0] ?? '',
                                'nama_lengkap' => $row[1] ?? '',
                                'tanggal_lahir' => $row[2] ?? '',
                                'jenis_kelamin' => strtolower($row[3] ?? ''),
                                'created_by' => session()->get('user_id')
                            ];

                            // Calculate age
                            if (!empty($data['tanggal_lahir'])) {
                                $data['umur'] = get_age_in_years($data['tanggal_lahir']);
                            }

                            $this->posbinduModel->save($data);
                            break;

                        case 'bumil':
                            $data = [
                                'nama_ibu' => $row[0] ?? '',
                                'nik_ibu' => $row[1] ?? '',
                                'nama_suami' => $row[2] ?? '',
                                'nik_suami' => $row[3] ?? '',
                                'no_kk' => $row[4] ?? '',
                                'no_bpjs' => $row[5] ?? '',
                                'no_hp' => $row[6] ?? '',
                                'alamat_lengkap' => $row[7] ?? '',
                                'usia_kehamilan_minggu' => !empty($row[8]) ? (int)$row[8] : null,
                                'status_aktif' => 'aktif',
                                'created_by' => session()->get('user_id')
                            ];

                            $this->bumilModel->save($data);
                            break;

                        case 'busui':
                            $data = [
                                'nama_ibu' => $row[0] ?? '',
                                'nik_ibu' => $row[1] ?? '',
                                'nama_ayah' => $row[2] ?? '',
                                'alamat' => $row[3] ?? '',
                                'no_hp_ibu' => $row[4] ?? '',
                                'nama_anak' => $row[5] ?? '',
                                'nik_anak' => $row[6] ?? '',
                                'tanggal_lahir_anak' => $row[7] ?? '',
                                'jenis_kelamin' => strtoupper($row[8] ?? ''),
                                'status_aktif' => 'aktif',
                                'created_by' => session()->get('user_id')
                            ];

                            // Validate jenis_kelamin
                            if (!in_array($data['jenis_kelamin'], ['L', 'P'])) {
                                $data['jenis_kelamin'] = 'L'; // Default to L if invalid
                            }

                            $this->busuiModel->save($data);
                            break;
                    }

                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                }
            }

            $message = "Import berhasil: {$successCount} data berhasil diimport";
            if ($errorCount > 0) {
                $message .= ", {$errorCount} data gagal diimport";
            }

            session()->setFlashdata('success', $message);

        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Error saat membaca file: ' . $e->getMessage());
        }

        return redirect()->to('/posyandu?tab=' . $type);
    }

    // Print report
    public function print($type)
    {
        if (!in_array($type, ['balita', 'remaja', 'lansia', 'posbindu', 'senam', 'toga', 'bumil', 'busui'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tipe data tidak valid');
        }

        $data = [];
        $title = 'Laporan Data ' . ucfirst($type);

        switch ($type) {
            case 'balita':
                $data = $this->getBalitaWithHealthData();
                break;
            case 'remaja':
                $data = $this->getRemajaWithHealthData();
                break;
            case 'lansia':
                $data = $this->getLansiaWithHealthData();
                break;
            case 'posbindu':
                $data = $this->getPosbinduWithHealthData();
                break;
            case 'bumil':
                $data = $this->bumilModel->orderBy('nama_ibu', 'ASC')->findAll();
                break;
            case 'busui':
                $data = $this->busuiModel->orderBy('nama_ibu', 'ASC')->findAll();
                break;
        }

        // Get profil RT settings
        $profil_rt = $this->settingsModel->getProfilRTSettings();

        return view('posyandu/print', [
            'title' => $title,
            'type' => $type,
            'data' => $data,
            'profil_rt' => $profil_rt
        ]);
    }

    // Get health data for each type
    private function getBalitaWithHealthData()
    {
        $balitaData = $this->balitaModel->findAll();

        foreach ($balitaData as &$balita) {
            // Get latest monthly record
            $monthlyRecord = $this->monthlyRecordModel
                ->where('person_id', $balita['id'])
                ->where('person_type', 'balita')
                ->orderBy('created_at', 'DESC')
                ->first();

            if ($monthlyRecord) {
                $balita['berat_badan'] = $monthlyRecord['berat_badan'];
                $balita['tinggi_badan'] = $monthlyRecord['tinggi_badan'];
                $balita['lingkar_lengan'] = $monthlyRecord['lingkar_lengan'];
                $balita['lingkar_kepala'] = $monthlyRecord['lingkar_kepala'];
                $balita['keterangan'] = $monthlyRecord['keterangan'];
                $balita['updated_at'] = $monthlyRecord['created_at'];
            } else {
                $balita['berat_badan'] = null;
                $balita['tinggi_badan'] = null;
                $balita['lingkar_lengan'] = null;
                $balita['lingkar_kepala'] = null;
                $balita['keterangan'] = null;
            }
        }

        return $balitaData;
    }

    private function getRemajaWithHealthData()
    {
        $remajaData = $this->remajaModel->findAll();

        foreach ($remajaData as &$remaja) {
            // Get latest monthly record
            $monthlyRecord = $this->monthlyRecordModel
                ->where('person_id', $remaja['id'])
                ->where('person_type', 'remaja')
                ->orderBy('created_at', 'DESC')
                ->first();

            if ($monthlyRecord) {
                $remaja['berat_badan'] = $monthlyRecord['berat_badan'];
                $remaja['tinggi_badan'] = $monthlyRecord['tinggi_badan'];
                $remaja['keterangan'] = $monthlyRecord['keterangan'];
                $remaja['updated_at'] = $monthlyRecord['created_at'];
            } else {
                $remaja['berat_badan'] = null;
                $remaja['tinggi_badan'] = null;
                $remaja['keterangan'] = null;
            }
        }

        return $remajaData;
    }

    private function getLansiaWithHealthData()
    {
        $lansiaData = $this->lansiaModel->findAll();

        foreach ($lansiaData as &$lansia) {
            // Get latest monthly record
            $monthlyRecord = $this->monthlyRecordModel
                ->where('person_id', $lansia['id'])
                ->where('person_type', 'lansia')
                ->orderBy('created_at', 'DESC')
                ->first();

            if ($monthlyRecord) {
                $lansia['berat_badan'] = $monthlyRecord['berat_badan'];
                $lansia['tinggi_badan'] = $monthlyRecord['tinggi_badan'];
                $lansia['keterangan'] = $monthlyRecord['keterangan'];
                $lansia['updated_at'] = $monthlyRecord['created_at'];
            } else {
                $lansia['berat_badan'] = null;
                $lansia['tinggi_badan'] = null;
                $lansia['keterangan'] = null;
            }
        }

        return $lansiaData;
    }

    private function getPosbinduWithHealthData()
    {
        $posbinduData = $this->posbinduModel->findAll();

        foreach ($posbinduData as &$posbindu) {
            // Get latest monthly record
            $monthlyRecord = $this->monthlyRecordModel
                ->where('person_id', $posbindu['id'])
                ->where('person_type', 'posbindu')
                ->orderBy('created_at', 'DESC')
                ->first();

            if ($monthlyRecord) {
                $posbindu['berat_badan'] = $monthlyRecord['berat_badan'];
                $posbindu['tinggi_badan'] = $monthlyRecord['tinggi_badan'];
                $posbindu['tensi'] = $monthlyRecord['tensi'];
                $posbindu['kolesterol'] = $monthlyRecord['kolesterol'];
                $posbindu['gula_darah'] = $monthlyRecord['gula_darah'];
                $posbindu['asam_urat'] = $monthlyRecord['asam_urat'];
                $posbindu['keterangan'] = $monthlyRecord['keterangan'];
                $posbindu['updated_at'] = $monthlyRecord['created_at'];
            } else {
                $posbindu['berat_badan'] = null;
                $posbindu['tinggi_badan'] = null;
                $posbindu['tensi'] = null;
                $posbindu['kolesterol'] = null;
                $posbindu['gula_darah'] = null;
                $posbindu['asam_urat'] = null;
                $posbindu['keterangan'] = null;
            }
        }

        return $posbinduData;
    }

    // Print detail report
    public function printDetail($id, $type)
    {
        if (!in_array($type, ['balita', 'remaja', 'lansia', 'posbindu'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tipe data tidak valid');
        }

        $person = null;
        $title = 'Detail Data ' . ucfirst($type);

        // Get person data based on type
        switch ($type) {
            case 'balita':
                $person = $this->balitaModel->find($id);
                break;
            case 'remaja':
                $person = $this->remajaModel->find($id);
                break;
            case 'lansia':
                $person = $this->lansiaModel->find($id);
                break;
            case 'posbindu':
                $person = $this->posbinduModel->find($id);
                break;
        }

        if (!$person) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data tidak ditemukan');
        }

        // Get monthly records
        $monthly_records = $this->monthlyRecordModel
            ->where('person_id', $id)
            ->where('person_type', $type)
            ->orderBy('record_date', 'DESC')
            ->findAll();

        // Get profil RT settings
        $profil_rt = $this->settingsModel->getProfilRTSettings();

        return view('posyandu/print_detail', [
            'title' => $title,
            'type' => $type,
            'person' => $person,
            'monthly_records' => $monthly_records,
            'profil_rt' => $profil_rt
        ]);
    }

    // Download import template
    public function downloadTemplate($type)
    {
        if (!in_array($type, ['balita', 'remaja', 'lansia', 'posbindu', 'senam', 'toga', 'bumil', 'busui'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Tipe data tidak valid');
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('SMART - Sistem Manajemen RT')
            ->setTitle('Template Import Data ' . ucfirst($type))
            ->setSubject('Template Import')
            ->setDescription('Template untuk import data ' . $type . ' ke sistem SMART');

        // Set column headers based on type
        $headers = [];
        switch ($type) {
            case 'balita':
                $headers = ['NIK', 'Nama Balita', 'Tanggal Lahir', 'Jenis Kelamin', 'Nama Ibu'];
                break;
            case 'remaja':
            case 'lansia':
            case 'posbindu':
                $headers = ['NIK', 'Nama Lengkap', 'Tanggal Lahir', 'Jenis Kelamin'];
                break;
            case 'senam':
                $headers = ['Nama Lengkap', 'NIK', 'Tanggal Daftar', 'Status', 'Keterangan'];
                break;
            case 'toga':
                $headers = ['Nama Kepala Keluarga', 'No. KK', 'Tanggal Daftar', 'Status', 'Keterangan'];
                break;
            case 'bumil':
                $headers = ['Nama Ibu', 'NIK Ibu', 'Nama Suami', 'NIK Suami', 'No KK', 'No BPJS', 'No HP', 'Alamat', 'Usia Kehamilan (minggu)'];
                break;
            case 'busui':
                $headers = ['Nama Ibu', 'NIK Ibu', 'Nama Ayah', 'Alamat', 'No HP Ibu', 'Nama Anak', 'NIK Anak', 'Tanggal Lahir Anak', 'Jenis Kelamin (L/P)'];
                break;
        }

        // Set headers in row 1
        foreach ($headers as $col => $header) {
            $cellAddress = chr(65 + $col) . '1';
            $sheet->setCellValue($cellAddress, $header);
            $sheet->getStyle($cellAddress)->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle($cellAddress)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
            $sheet->getStyle($cellAddress)->getFill()->getStartColor()->setARGB('FF4472C4');
            $sheet->getStyle($cellAddress)->getFont()->getColor()->setARGB('FFFFFFFF');
            $sheet->getStyle($cellAddress)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle($cellAddress)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
        }

        // Add sample data starting from row 2
        $sampleData = [];
        switch ($type) {
            case 'balita':
                $sampleData = [
                    ['3201012345678901', 'Ahmad Wijaya', '2020-01-15', 'laki-laki', 'Siti Aminah'],
                    ['3201012345678902', 'Sari Dewi', '2019-06-20', 'perempuan', 'Rina Sari']
                ];
                break;
            case 'remaja':
                $sampleData = [
                    ['3201012345678903', 'Budi Santoso', '2008-03-10', 'laki-laki'],
                    ['3201012345678904', 'Maya Sari', '2007-11-25', 'perempuan']
                ];
                break;
            case 'lansia':
                $sampleData = [
                    ['3201012345678905', 'Pak Harto', '1955-08-17', 'laki-laki'],
                    ['3201012345678906', 'Bu Siti', '1960-12-05', 'perempuan']
                ];
                break;
            case 'posbindu':
                $sampleData = [
                    ['3201012345678907', 'Pak Joko', '1970-04-12', 'laki-laki'],
                    ['3201012345678908', 'Bu Ani', '1975-09-30', 'perempuan']
                ];
                break;
            case 'senam':
                $sampleData = [
                    ['Siti Rahayu', '3201012345678909', '2025-01-15', 'aktif', 'Peserta rutin senam pagi'],
                    ['Budi Hartono', '3201012345678910', '2025-01-20', 'aktif', 'Peserta baru']
                ];
                break;
            case 'toga':
                $sampleData = [
                    ['Pak Bambang', '3201012345678911', '2025-01-10', 'aktif', 'Menanam cabai dan tomat'],
                    ['Bu Sari', '3201012345678912', '2025-01-25', 'aktif', 'Menanam sayuran hijau']
                ];
                break;
        }

        // Fill sample data
        foreach ($sampleData as $rowIndex => $rowData) {
            $row = $rowIndex + 2; // Start from row 2
            foreach ($rowData as $colIndex => $cellData) {
                $cellAddress = chr(65 + $colIndex) . $row;
                $sheet->setCellValue($cellAddress, $cellData);
                $sheet->getStyle($cellAddress)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
            }
        }

        // Auto-size columns
        foreach (range('A', chr(64 + count($headers))) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Set row height for header
        $sheet->getRowDimension(1)->setRowHeight(25);

        $filename = 'Template_Import_' . ucfirst($type) . '_' . date('Y-m-d') . '.xlsx';

        $writer = new Xlsx($spreadsheet);

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    // ==================== BUMIL METHODS ====================

    public function createBumil()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $data = [
            'title' => 'Tambah Data Ibu Hamil',
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/create', $data);
    }

    public function storeBumil()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $rules = [
            'nama_ibu' => 'required|max_length[100]',
            'nik_ibu' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_ibu' => 'permit_empty|valid_date',
            'nama_suami' => 'permit_empty|max_length[100]',
            'nik_suami' => 'permit_empty|exact_length[16]|numeric',
            'no_kk' => 'permit_empty|exact_length[16]|numeric',
            'no_bpjs' => 'permit_empty|max_length[20]',
            'no_hp' => 'permit_empty|max_length[20]',
            'alamat_lengkap' => 'permit_empty',
            'usia_kehamilan_minggu' => 'permit_empty|integer|greater_than_equal_to[0]|less_than_equal_to[42]',
            'lila' => 'permit_empty|decimal',
            'bb' => 'permit_empty|decimal',
            'tb' => 'permit_empty|decimal',
            'td_sistol' => 'permit_empty|integer|greater_than[0]',
            'td_diastol' => 'permit_empty|integer|greater_than[0]',
            'hpht' => 'permit_empty|valid_date',
            'htp' => 'permit_empty|valid_date',
            'jarak_anak_tahun' => 'permit_empty|integer|greater_than_equal_to[0]',
            'jumlah_anak' => 'permit_empty|integer|greater_than_equal_to[0]',
            'tfu' => 'permit_empty|decimal',
            'status_aktif' => 'required|in_list[aktif,tidak_aktif,melahirkan]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'nik_ibu' => $this->request->getPost('nik_ibu'),
            'tanggal_lahir_ibu' => $this->request->getPost('tanggal_lahir_ibu'),
            'nama_suami' => $this->request->getPost('nama_suami'),
            'nik_suami' => $this->request->getPost('nik_suami'),
            'no_kk' => $this->request->getPost('no_kk'),
            'no_bpjs' => $this->request->getPost('no_bpjs'),
            'no_hp' => $this->request->getPost('no_hp'),
            'alamat_lengkap' => $this->request->getPost('alamat_lengkap'),
            'usia_kehamilan_minggu' => $this->request->getPost('usia_kehamilan_minggu'),
            'lila' => $this->request->getPost('lila'),
            'bb' => $this->request->getPost('bb'),
            'tb' => $this->request->getPost('tb'),
            'td_sistol' => $this->request->getPost('td_sistol'),
            'td_diastol' => $this->request->getPost('td_diastol'),
            'hpht' => $this->request->getPost('hpht'),
            'htp' => $this->request->getPost('htp'),
            'jarak_anak_tahun' => $this->request->getPost('jarak_anak_tahun'),
            'jumlah_anak' => $this->request->getPost('jumlah_anak'),
            'tfu' => $this->request->getPost('tfu'),
            'status_aktif' => $this->request->getPost('status_aktif'),
            'keterangan' => $this->request->getPost('keterangan'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->bumilModel->insert($data)) {
            return redirect()->to('/posyandu?tab=bumil')->with('success', 'Data ibu hamil berhasil ditambahkan');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan data ibu hamil');
        }
    }

    public function editBumil($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $bumil = $this->bumilModel->find($id);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Ibu Hamil',
            'bumil' => $bumil,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/edit', $data);
    }

    public function updateBumil($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $bumil = $this->bumilModel->find($id);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        $rules = [
            'nama_ibu' => 'required|max_length[100]',
            'nik_ibu' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_ibu' => 'permit_empty|valid_date',
            'nama_suami' => 'permit_empty|max_length[100]',
            'nik_suami' => 'permit_empty|exact_length[16]|numeric',
            'no_kk' => 'permit_empty|exact_length[16]|numeric',
            'no_bpjs' => 'permit_empty|max_length[20]',
            'no_hp' => 'permit_empty|max_length[20]',
            'alamat_lengkap' => 'permit_empty',
            'usia_kehamilan_minggu' => 'permit_empty|integer|greater_than_equal_to[0]|less_than_equal_to[42]',
            'lila' => 'permit_empty|decimal',
            'bb' => 'permit_empty|decimal',
            'tb' => 'permit_empty|decimal',
            'td_sistol' => 'permit_empty|integer|greater_than[0]',
            'td_diastol' => 'permit_empty|integer|greater_than[0]',
            'hpht' => 'permit_empty|valid_date',
            'htp' => 'permit_empty|valid_date',
            'jarak_anak_tahun' => 'permit_empty|integer|greater_than_equal_to[0]',
            'jumlah_anak' => 'permit_empty|integer|greater_than_equal_to[0]',
            'tfu' => 'permit_empty|decimal',
            'status_aktif' => 'required|in_list[aktif,tidak_aktif,melahirkan]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'nik_ibu' => $this->request->getPost('nik_ibu'),
            'tanggal_lahir_ibu' => $this->request->getPost('tanggal_lahir_ibu'),
            'nama_suami' => $this->request->getPost('nama_suami'),
            'nik_suami' => $this->request->getPost('nik_suami'),
            'no_kk' => $this->request->getPost('no_kk'),
            'no_bpjs' => $this->request->getPost('no_bpjs'),
            'no_hp' => $this->request->getPost('no_hp'),
            'alamat_lengkap' => $this->request->getPost('alamat_lengkap'),
            'usia_kehamilan_minggu' => $this->request->getPost('usia_kehamilan_minggu'),
            'lila' => $this->request->getPost('lila'),
            'bb' => $this->request->getPost('bb'),
            'tb' => $this->request->getPost('tb'),
            'td_sistol' => $this->request->getPost('td_sistol'),
            'td_diastol' => $this->request->getPost('td_diastol'),
            'hpht' => $this->request->getPost('hpht'),
            'htp' => $this->request->getPost('htp'),
            'jarak_anak_tahun' => $this->request->getPost('jarak_anak_tahun'),
            'jumlah_anak' => $this->request->getPost('jumlah_anak'),
            'tfu' => $this->request->getPost('tfu'),
            'status_aktif' => $this->request->getPost('status_aktif'),
            'keterangan' => $this->request->getPost('keterangan'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->bumilModel->update($id, $data)) {
            return redirect()->to('/posyandu?tab=bumil')->with('success', 'Data ibu hamil berhasil diupdate');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal mengupdate data ibu hamil');
        }
    }

    public function deleteBumil($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $bumil = $this->bumilModel->find($id);
        if (!$bumil) {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Data bumil tidak ditemukan');
        }

        if ($this->bumilModel->delete($id)) {
            return redirect()->to('/posyandu?tab=bumil')->with('success', 'Data ibu hamil berhasil dihapus');
        } else {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Gagal menghapus data ibu hamil');
        }
    }

    public function detailBumil($id)
    {
        $bumil = $this->bumilModel->find($id);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        // Get monthly records
        $monthlyRecords = $this->bumilMonthlyRecordModel->getRecordsByBumilId($id);

        $data = [
            'title' => 'Detail Ibu Hamil',
            'bumil' => $bumil,
            'monthlyRecords' => $monthlyRecords,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/detail', $data);
    }

    // ==================== BUSUI METHODS ====================

    public function createBusui()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $data = [
            'title' => 'Tambah Data Ibu Menyusui',
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/create', $data);
    }

    public function storeBusui()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $rules = [
            'nama_ibu' => 'required|max_length[100]',
            'nik_ibu' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_ibu' => 'permit_empty|valid_date',
            'nama_ayah' => 'permit_empty|max_length[100]',
            'pekerjaan_ayah' => 'permit_empty|max_length[100]',
            'alamat' => 'permit_empty',
            'no_hp_ibu' => 'permit_empty|max_length[20]',
            'nama_anak' => 'required|max_length[100]',
            'nik_anak' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_anak' => 'required|valid_date',
            'jenis_kelamin' => 'required|in_list[L,P]',
            'bb_anak' => 'permit_empty|decimal',
            'tb_anak' => 'permit_empty|decimal',
            'status_aktif' => 'required|in_list[aktif,tidak_aktif,selesai]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'nik_ibu' => $this->request->getPost('nik_ibu'),
            'tanggal_lahir_ibu' => $this->request->getPost('tanggal_lahir_ibu'),
            'nama_ayah' => $this->request->getPost('nama_ayah'),
            'pekerjaan_ayah' => $this->request->getPost('pekerjaan_ayah'),
            'alamat' => $this->request->getPost('alamat'),
            'no_hp_ibu' => $this->request->getPost('no_hp_ibu'),
            'nama_anak' => $this->request->getPost('nama_anak'),
            'nik_anak' => $this->request->getPost('nik_anak'),
            'tanggal_lahir_anak' => $this->request->getPost('tanggal_lahir_anak'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'bb_anak' => $this->request->getPost('bb_anak'),
            'tb_anak' => $this->request->getPost('tb_anak'),
            'status_aktif' => $this->request->getPost('status_aktif'),
            'keterangan' => $this->request->getPost('keterangan'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->busuiModel->insert($data)) {
            return redirect()->to('/posyandu?tab=busui')->with('success', 'Data ibu menyusui berhasil ditambahkan');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan data ibu menyusui');
        }
    }

    public function editBusui($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $busui = $this->busuiModel->find($id);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Ibu Menyusui',
            'busui' => $busui,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/edit', $data);
    }

    public function updateBusui($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $busui = $this->busuiModel->find($id);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        $rules = [
            'nama_ibu' => 'required|max_length[100]',
            'nik_ibu' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_ibu' => 'permit_empty|valid_date',
            'nama_ayah' => 'permit_empty|max_length[100]',
            'pekerjaan_ayah' => 'permit_empty|max_length[100]',
            'alamat' => 'permit_empty',
            'no_hp_ibu' => 'permit_empty|max_length[20]',
            'nama_anak' => 'required|max_length[100]',
            'nik_anak' => 'permit_empty|exact_length[16]|numeric',
            'tanggal_lahir_anak' => 'required|valid_date',
            'jenis_kelamin' => 'required|in_list[L,P]',
            'bb_anak' => 'permit_empty|decimal',
            'tb_anak' => 'permit_empty|decimal',
            'status_aktif' => 'required|in_list[aktif,tidak_aktif,selesai]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'nama_ibu' => $this->request->getPost('nama_ibu'),
            'nik_ibu' => $this->request->getPost('nik_ibu'),
            'tanggal_lahir_ibu' => $this->request->getPost('tanggal_lahir_ibu'),
            'nama_ayah' => $this->request->getPost('nama_ayah'),
            'pekerjaan_ayah' => $this->request->getPost('pekerjaan_ayah'),
            'alamat' => $this->request->getPost('alamat'),
            'no_hp_ibu' => $this->request->getPost('no_hp_ibu'),
            'nama_anak' => $this->request->getPost('nama_anak'),
            'nik_anak' => $this->request->getPost('nik_anak'),
            'tanggal_lahir_anak' => $this->request->getPost('tanggal_lahir_anak'),
            'jenis_kelamin' => $this->request->getPost('jenis_kelamin'),
            'bb_anak' => $this->request->getPost('bb_anak'),
            'tb_anak' => $this->request->getPost('tb_anak'),
            'status_aktif' => $this->request->getPost('status_aktif'),
            'keterangan' => $this->request->getPost('keterangan'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->busuiModel->update($id, $data)) {
            return redirect()->to('/posyandu?tab=busui')->with('success', 'Data ibu menyusui berhasil diupdate');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal mengupdate data ibu menyusui');
        }
    }

    public function deleteBusui($id)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $busui = $this->busuiModel->find($id);
        if (!$busui) {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Data busui tidak ditemukan');
        }

        if ($this->busuiModel->delete($id)) {
            return redirect()->to('/posyandu?tab=busui')->with('success', 'Data ibu menyusui berhasil dihapus');
        } else {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Gagal menghapus data ibu menyusui');
        }
    }

    public function detailBusui($id)
    {
        $busui = $this->busuiModel->find($id);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        // Get monthly records
        $monthlyRecords = $this->busuiMonthlyRecordModel->getRecordsByBusuiId($id);

        $data = [
            'title' => 'Detail Ibu Menyusui',
            'busui' => $busui,
            'monthlyRecords' => $monthlyRecords,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/detail', $data);
    }

    // ==================== BUMIL MONTHLY RECORDS METHODS ====================

    public function addBumilRecord($bumilId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $bumil = $this->bumilModel->find($bumilId);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        $data = [
            'title' => 'Tambah Data Kehamilan',
            'bumil' => $bumil,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/add_record', $data);
    }

    public function storeBumilRecord($bumilId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $bumil = $this->bumilModel->find($bumilId);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        $rules = [
            'tanggal_periksa' => 'required|valid_date',
            'usia_kehamilan_minggu' => 'permit_empty|integer|greater_than_equal_to[0]|less_than_equal_to[42]',
            'berat_badan' => 'permit_empty|decimal',
            'tinggi_badan' => 'permit_empty|decimal',
            'lila' => 'permit_empty|decimal',
            'tfu' => 'permit_empty|decimal',
            'td_sistol' => 'permit_empty|integer|greater_than[0]',
            'td_diastol' => 'permit_empty|integer|greater_than[0]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if record already exists for this date
        if ($this->bumilMonthlyRecordModel->recordExistsForDate($bumilId, $this->request->getPost('tanggal_periksa'))) {
            return redirect()->back()->withInput()->with('error', 'Data untuk tanggal ini sudah ada. Silakan edit data yang sudah ada atau pilih tanggal lain.');
        }

        $data = [
            'bumil_id' => $bumilId,
            'tanggal_periksa' => $this->request->getPost('tanggal_periksa'),
            'usia_kehamilan_minggu' => $this->request->getPost('usia_kehamilan_minggu'),
            'berat_badan' => $this->request->getPost('berat_badan'),
            'tinggi_badan' => $this->request->getPost('tinggi_badan'),
            'lila' => $this->request->getPost('lila'),
            'tfu' => $this->request->getPost('tfu'),
            'td_sistol' => $this->request->getPost('td_sistol'),
            'td_diastol' => $this->request->getPost('td_diastol'),
            'keluhan' => $this->request->getPost('keluhan'),
            'keterangan' => $this->request->getPost('keterangan'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->bumilMonthlyRecordModel->insert($data)) {
            return redirect()->to('/posyandu/bumil/detail/' . $bumilId)->with('success', 'Data kehamilan berhasil ditambahkan');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan data kehamilan');
        }
    }

    public function editBumilRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $record = $this->bumilMonthlyRecordModel->find($recordId);
        if (!$record) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data record tidak ditemukan');
        }

        $bumil = $this->bumilModel->find($record['bumil_id']);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Kehamilan',
            'bumil' => $bumil,
            'record' => $record,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/edit_record', $data);
    }

    public function updateBumilRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $record = $this->bumilMonthlyRecordModel->find($recordId);
        if (!$record) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data record tidak ditemukan');
        }

        $rules = [
            'tanggal_periksa' => 'required|valid_date',
            'usia_kehamilan_minggu' => 'permit_empty|integer|greater_than_equal_to[0]|less_than_equal_to[42]',
            'berat_badan' => 'permit_empty|decimal',
            'tinggi_badan' => 'permit_empty|decimal',
            'lila' => 'permit_empty|decimal',
            'tfu' => 'permit_empty|decimal',
            'td_sistol' => 'permit_empty|integer|greater_than[0]',
            'td_diastol' => 'permit_empty|integer|greater_than[0]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if record already exists for this date (excluding current record)
        $existingRecord = $this->bumilMonthlyRecordModel->where('bumil_id', $record['bumil_id'])
                                                        ->where('tanggal_periksa', $this->request->getPost('tanggal_periksa'))
                                                        ->where('id !=', $recordId)
                                                        ->first();

        if ($existingRecord) {
            return redirect()->back()->withInput()->with('error', 'Data untuk tanggal ini sudah ada. Silakan pilih tanggal lain.');
        }

        $data = [
            'tanggal_periksa' => $this->request->getPost('tanggal_periksa'),
            'usia_kehamilan_minggu' => $this->request->getPost('usia_kehamilan_minggu'),
            'berat_badan' => $this->request->getPost('berat_badan'),
            'tinggi_badan' => $this->request->getPost('tinggi_badan'),
            'lila' => $this->request->getPost('lila'),
            'tfu' => $this->request->getPost('tfu'),
            'td_sistol' => $this->request->getPost('td_sistol'),
            'td_diastol' => $this->request->getPost('td_diastol'),
            'keluhan' => $this->request->getPost('keluhan'),
            'keterangan' => $this->request->getPost('keterangan'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->bumilMonthlyRecordModel->update($recordId, $data)) {
            return redirect()->to('/posyandu/bumil/detail/' . $record['bumil_id'])->with('success', 'Data kehamilan berhasil diupdate');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal mengupdate data kehamilan');
        }
    }

    public function deleteBumilRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=bumil')->with('error', 'Akses ditolak!');
        }

        $record = $this->bumilMonthlyRecordModel->find($recordId);
        if (!$record) {
            return redirect()->back()->with('error', 'Data record tidak ditemukan');
        }

        if ($this->bumilMonthlyRecordModel->delete($recordId)) {
            return redirect()->to('/posyandu/bumil/detail/' . $record['bumil_id'])->with('success', 'Data kehamilan berhasil dihapus');
        } else {
            return redirect()->back()->with('error', 'Gagal menghapus data kehamilan');
        }
    }

    // ==================== BUSUI MONTHLY RECORDS METHODS ====================

    public function addBusuiRecord($busuiId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $busui = $this->busuiModel->find($busuiId);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        $data = [
            'title' => 'Tambah Data Kesehatan Anak',
            'busui' => $busui,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/add_record', $data);
    }

    public function storeBusuiRecord($busuiId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $busui = $this->busuiModel->find($busuiId);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        $rules = [
            'tanggal_periksa' => 'required|valid_date',
            'berat_badan_anak' => 'permit_empty|decimal',
            'tinggi_badan_anak' => 'permit_empty|decimal',
            'lingkar_kepala_anak' => 'permit_empty|decimal',
            'lingkar_lengan_anak' => 'permit_empty|decimal',
            'status_gizi' => 'permit_empty|in_list[baik,kurang,buruk,lebih]',
            'asi_eksklusif' => 'permit_empty|in_list[ya,tidak]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if record already exists for this date
        if ($this->busuiMonthlyRecordModel->recordExistsForDate($busuiId, $this->request->getPost('tanggal_periksa'))) {
            return redirect()->back()->withInput()->with('error', 'Data untuk tanggal ini sudah ada. Silakan edit data yang sudah ada atau pilih tanggal lain.');
        }

        $data = [
            'busui_id' => $busuiId,
            'tanggal_periksa' => $this->request->getPost('tanggal_periksa'),
            'berat_badan_anak' => $this->request->getPost('berat_badan_anak'),
            'tinggi_badan_anak' => $this->request->getPost('tinggi_badan_anak'),
            'lingkar_kepala_anak' => $this->request->getPost('lingkar_kepala_anak'),
            'lingkar_lengan_anak' => $this->request->getPost('lingkar_lengan_anak'),
            'status_gizi' => $this->request->getPost('status_gizi'),
            'asi_eksklusif' => $this->request->getPost('asi_eksklusif'),
            'imunisasi' => $this->request->getPost('imunisasi'),
            'keluhan' => $this->request->getPost('keluhan'),
            'keterangan' => $this->request->getPost('keterangan'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->busuiMonthlyRecordModel->insert($data)) {
            return redirect()->to('/posyandu/busui/detail/' . $busuiId)->with('success', 'Data kesehatan anak berhasil ditambahkan');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal menambahkan data kesehatan anak');
        }
    }

    public function editBusuiRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $record = $this->busuiMonthlyRecordModel->find($recordId);
        if (!$record) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data record tidak ditemukan');
        }

        $busui = $this->busuiModel->find($record['busui_id']);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        $data = [
            'title' => 'Edit Data Kesehatan Anak',
            'busui' => $busui,
            'record' => $record,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/edit_record', $data);
    }

    public function updateBusuiRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $record = $this->busuiMonthlyRecordModel->find($recordId);
        if (!$record) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data record tidak ditemukan');
        }

        $rules = [
            'tanggal_periksa' => 'required|valid_date',
            'berat_badan_anak' => 'permit_empty|decimal',
            'tinggi_badan_anak' => 'permit_empty|decimal',
            'lingkar_kepala_anak' => 'permit_empty|decimal',
            'lingkar_lengan_anak' => 'permit_empty|decimal',
            'status_gizi' => 'permit_empty|in_list[baik,kurang,buruk,lebih]',
            'asi_eksklusif' => 'permit_empty|in_list[ya,tidak]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if record already exists for this date (excluding current record)
        $existingRecord = $this->busuiMonthlyRecordModel->where('busui_id', $record['busui_id'])
                                                         ->where('tanggal_periksa', $this->request->getPost('tanggal_periksa'))
                                                         ->where('id !=', $recordId)
                                                         ->first();

        if ($existingRecord) {
            return redirect()->back()->withInput()->with('error', 'Data untuk tanggal ini sudah ada. Silakan pilih tanggal lain.');
        }

        $data = [
            'tanggal_periksa' => $this->request->getPost('tanggal_periksa'),
            'berat_badan_anak' => $this->request->getPost('berat_badan_anak'),
            'tinggi_badan_anak' => $this->request->getPost('tinggi_badan_anak'),
            'lingkar_kepala_anak' => $this->request->getPost('lingkar_kepala_anak'),
            'lingkar_lengan_anak' => $this->request->getPost('lingkar_lengan_anak'),
            'status_gizi' => $this->request->getPost('status_gizi'),
            'asi_eksklusif' => $this->request->getPost('asi_eksklusif'),
            'imunisasi' => $this->request->getPost('imunisasi'),
            'keluhan' => $this->request->getPost('keluhan'),
            'keterangan' => $this->request->getPost('keterangan'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->busuiMonthlyRecordModel->update($recordId, $data)) {
            return redirect()->to('/posyandu/busui/detail/' . $record['busui_id'])->with('success', 'Data kesehatan anak berhasil diupdate');
        } else {
            return redirect()->back()->withInput()->with('error', 'Gagal mengupdate data kesehatan anak');
        }
    }

    public function deleteBusuiRecord($recordId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'admin') {
            return redirect()->to('/posyandu?tab=busui')->with('error', 'Akses ditolak!');
        }

        $record = $this->busuiMonthlyRecordModel->find($recordId);
        if (!$record) {
            return redirect()->back()->with('error', 'Data record tidak ditemukan');
        }

        if ($this->busuiMonthlyRecordModel->delete($recordId)) {
            return redirect()->to('/posyandu/busui/detail/' . $record['busui_id'])->with('success', 'Data kesehatan anak berhasil dihapus');
        } else {
            return redirect()->back()->with('error', 'Gagal menghapus data kesehatan anak');
        }
    }

    // ==================== PRINT DETAIL METHODS ====================

    public function printDetailBumil($id)
    {
        $bumil = $this->bumilModel->find($id);
        if (!$bumil) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data bumil tidak ditemukan');
        }

        // Get monthly records
        $monthlyRecords = $this->bumilMonthlyRecordModel->getRecordsByBumilId($id);

        $data = [
            'title' => 'Detail Ibu Hamil',
            'bumil' => $bumil,
            'monthlyRecords' => $monthlyRecords,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/bumil/print_detail', $data);
    }

    public function printDetailBusui($id)
    {
        $busui = $this->busuiModel->find($id);
        if (!$busui) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Data busui tidak ditemukan');
        }

        // Get monthly records
        $monthlyRecords = $this->busuiMonthlyRecordModel->getRecordsByBusuiId($id);

        $data = [
            'title' => 'Detail Ibu Menyusui',
            'busui' => $busui,
            'monthlyRecords' => $monthlyRecords,
            'profil_rt' => $this->settingsModel->getProfilRTSettings()
        ];

        return view('posyandu/busui/print_detail', $data);
    }
}
